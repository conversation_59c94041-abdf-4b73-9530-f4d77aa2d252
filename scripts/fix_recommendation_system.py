#!/usr/bin/env python3
"""
推荐系统快速修复与验证脚本

用法示例:
    python scripts/fix_recommendation_system.py --stage 1
    python scripts/fix_recommendation_system.py --stage 2
    python scripts/fix_recommendation_system.py --stage 3
    python scripts/fix_recommendation_system.py --stage all

脚本职责:
1. 对核心代码缺口进行静态扫描，帮助快速定位阻断问题。
2. 初始化推荐相关的 Redis 数据结构与群组映射。
3. 基于实际标签/分类生成相似度数据，并验证整体数据健康。
4. 输出 JSON 形式的修复报告，便于归档与追踪。
"""

from __future__ import annotations

import argparse
import asyncio
import json
import logging
import re
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Iterable

# === Python 路径设置 ===
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.append(str(PROJECT_ROOT))

# === 第三方 / 项目内依赖 ===
from app.crud import article as crud_article
from app.crud import user as crud_user
from app.crud import user_behavior as crud_user_behavior
from app.crud import video as crud_video
from app.db.redis import delete_key, hash_get_all, hash_set, sorted_set_add, sorted_set_get_range
from app.db.session import SessionLocal

# === 日志配置 ===
logger = logging.getLogger("recommendation_fix")
logging.basicConfig(level=logging.INFO, format="%(message)s")

REPORT_PATH = PROJECT_ROOT / "docs" / "recommendation_fix_report.json"


class RecommendationSystemFixer:
    """推荐系统数据修复与校验工具"""

    def __init__(self) -> None:
        self.db = None
        self.report: dict[str, Any] = {
            "generated_at": None,
            "stage1": {},
            "stage2": {},
            "stage3": {},
        }

    # ------------------------------------------------------------------
    # 上下文管理
    # ------------------------------------------------------------------
    async def __aenter__(self) -> "RecommendationSystemFixer":
        self.db = SessionLocal()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        if self.db is not None:
            await self.db.close()
            self.db = None

    # ------------------------------------------------------------------
    # Stage 1: 阻断问题排查与基础清理
    # ------------------------------------------------------------------
    async def fix_stage_1(self) -> None:
        logger.info("🔧 开始阶段 1：阻断问题排查与安全加固")

        code_checks = self._check_code_issues()
        self.report["stage1"]["code_checks"] = code_checks

        cleanup_results = await self._initialize_basic_structures()
        self.report["stage1"]["redis_cleanup"] = cleanup_results

        logger.info("✅ 阶段 1 完成\n")

    # ------------------------------------------------------------------
    # Stage 2: 架构闭环与召回数据初始化
    # ------------------------------------------------------------------
    async def fix_stage_2(self) -> None:
        logger.info("🔧 开始阶段 2：数据结构与召回闭环初始化")

        group_stats = await self._initialize_user_groups()
        self.report["stage2"]["user_groups"] = group_stats

        pool_stats = await self._initialize_recommendation_pools()
        self.report["stage2"]["rec_pools"] = pool_stats

        similarity_stats = await self._initialize_similarity_data()
        self.report["stage2"]["similarity"] = similarity_stats

        logger.info("✅ 阶段 2 完成\n")

    # ------------------------------------------------------------------
    # Stage 3: 数据验证与报告输出
    # ------------------------------------------------------------------
    async def fix_stage_3(self) -> None:
        logger.info("🔧 开始阶段 3：数据完整性校验与报告生成")

        integrity = await self._validate_data_integrity()
        self.report["stage3"]["integrity_checks"] = integrity

        await self._generate_fix_report()

        logger.info("✅ 阶段 3 完成\n")

    # ------------------------------------------------------------------
    # 代码检查
    # ------------------------------------------------------------------
    def _check_code_issues(self) -> list[dict[str, Any]]:
        """静态检查关键方法/危险用法，辅助开发者快速定位阻断点"""
        logger.info("📋 正在扫描核心代码缺口...")

        targets: list[tuple[str, str | None, str]] = [
            ("app/services/recommendation_service.py", "record_feedback", "补齐反馈记录逻辑"),
            (
                "app/services/recommendation_service.py",
                "update_user_profile_from_feedback",
                "基于反馈的画像更新",
            ),
            ("app/services/recommendation_cache_service.py", "clear_user_cache", "新增缓存清理入口"),
            ("app/tasks/recommendation_tasks.py", None, "确认已移除 eval() 调用"),
        ]

        results: list[dict[str, Any]] = []
        for path, method, description in targets:
            text = self._read_source(path)
            if not text:
                results.append(
                    {
                        "target": path if method is None else f"{path}::{method}",
                        "status": "missing",
                        "description": "文件不存在",
                    }
                )
                logger.warning("❌ %s: 文件不存在", path)
                continue

            if method:
                pattern = re.compile(rf"async\s+def\s+{method}\b")
                exists = bool(pattern.search(text))
                status = "present" if exists else "missing"
                icon = "✅" if exists else "❌"
                logger.info("%s %s - %s", icon, f"{path}::{method}", description)
                results.append(
                    {
                        "target": f"{path}::{method}",
                        "status": status,
                        "description": description,
                    }
                )
            else:
                has_eval = "eval(" in text
                status = "unsafe" if has_eval else "clean"
                icon = "❌" if has_eval else "✅"
                logger.info("%s %s - %s", icon, path, description)
                results.append(
                    {
                        "target": path,
                        "status": status,
                        "description": description,
                    }
                )

        return results

    # ------------------------------------------------------------------
    # Redis 基础结构清理
    # ------------------------------------------------------------------
    async def _initialize_basic_structures(self) -> dict[str, Any]:
        logger.info("🏗️  正在清理热门/最新推荐池旧数据...")
        keys_to_reset = [
            "rec_pool:hot:article",
            "rec_pool:hot:video",
            "rec_pool:latest:article",
            "rec_pool:latest:video",
        ]
        removed: list[str] = []
        for key in keys_to_reset:
            await delete_key(key)
            removed.append(key)
            logger.info("   🧹 已删除 Redis Key: %s", key)
        return {"cleared_keys": removed}

    # ------------------------------------------------------------------
    # 用户群组初始化
    # ------------------------------------------------------------------
    async def _initialize_user_groups(self) -> dict[str, Any]:
        logger.info("👥 正在初始化用户群组映射 user_to_group ...")
        users = await crud_user.get_multi(self.db, limit=50_000)
        user_count = len(users)
        group_count = max(5, min(50, user_count // 200 or 1))

        await delete_key("user_to_group")

        for index, user in enumerate(users):
            group_id = index % group_count
            await hash_set("user_to_group", str(user.id), str(group_id))
            if (index + 1) % 1000 == 0:
                logger.info("   已处理用户 %s/%s", index + 1, user_count)

        logger.info("   ✅ 完成，%s 名用户划分到 %s 个群组", user_count, group_count)
        return {"user_count": user_count, "group_count": group_count}

    # ------------------------------------------------------------------
    # 推荐池初始化
    # ------------------------------------------------------------------
    async def _initialize_recommendation_pools(self) -> dict[str, Any]:
        logger.info("🎯 正在初始化推荐池(热门/最新/群组)...")

        hot_stats = await self._init_hot_pools()
        latest_stats = await self._init_latest_pools()
        group_stats = await self._init_group_pools()

        return {
            "hot": hot_stats,
            "latest": latest_stats,
            "group": group_stats,
        }

    async def _init_hot_pools(self) -> dict[str, Any]:
        logger.info("   🔥 填充热门推荐池 ...")
        stats: dict[str, Any] = {"articles": 0, "videos": 0}
        try:
            hot_articles = await crud_user_behavior.user_browse_history.get_popular_content(
                self.db, content_type="article", limit=500, days=7
            )
            hot_videos = await crud_user_behavior.user_browse_history.get_popular_content(
                self.db, content_type="video", limit=500, days=7
            )

            if hot_articles:
                mapping = {str(item.content_id): float(item.view_count) for item in hot_articles}
                await delete_key("rec_pool:hot:article")
                await sorted_set_add("rec_pool:hot:article", mapping)
                stats["articles"] = len(mapping)

            if hot_videos:
                mapping = {str(item.content_id): float(item.view_count) for item in hot_videos}
                await delete_key("rec_pool:hot:video")
                await sorted_set_add("rec_pool:hot:video", mapping)
                stats["videos"] = len(mapping)

            logger.info("     ✅ 热门池写入 - 文章 %s 条, 视频 %s 条", stats["articles"], stats["videos"])
        except Exception as exc:  # noqa: BLE001
            logger.warning("     ⚠️ 热门池计算失败，使用备用方案: %s", exc)
            stats = await self._init_hot_pools_fallback()
        return stats

    async def _init_hot_pools_fallback(self) -> dict[str, Any]:
        logger.info("     🔄 使用最新内容作为热门池兜底")
        articles = await crud_article.get_published(self.db, limit=100)
        videos = await crud_video.get_multi(self.db, limit=100)

        stats = {"articles": 0, "videos": 0}
        if articles:
            mapping = {str(a.id): float(a.id) for a in articles}
            await delete_key("rec_pool:hot:article")
            await sorted_set_add("rec_pool:hot:article", mapping)
            stats["articles"] = len(mapping)
        if videos:
            mapping = {str(v.id): float(v.id) for v in videos}
            await delete_key("rec_pool:hot:video")
            await sorted_set_add("rec_pool:hot:video", mapping)
            stats["videos"] = len(mapping)
        logger.info("     ✅ 备用热门池写入 - 文章 %s 条, 视频 %s 条", stats["articles"], stats["videos"])
        return stats

    async def _init_latest_pools(self) -> dict[str, Any]:
        logger.info("   🆕 填充最新推荐池 ...")
        articles = await crud_article.get_published(self.db, limit=1000)
        videos = await crud_video.get_multi(self.db, limit=1000)
        stats = {"articles": 0, "videos": 0}

        if articles:
            mapping = {str(article.id): article.created_at.timestamp() for article in articles}
            await delete_key("rec_pool:latest:article")
            await sorted_set_add("rec_pool:latest:article", mapping)
            stats["articles"] = len(mapping)

        if videos:
            mapping = {str(video.id): video.created_at.timestamp() for video in videos}
            await delete_key("rec_pool:latest:video")
            await sorted_set_add("rec_pool:latest:video", mapping)
            stats["videos"] = len(mapping)

        logger.info(
            "     ✅ 最新池写入 - 文章 %s 条, 视频 %s 条",
            stats["articles"],
            stats["videos"],
        )
        return stats

    async def _init_group_pools(self) -> dict[str, Any]:
        logger.info("   👥 按群组填充推荐池 ...")
        user_groups = await hash_get_all("user_to_group")
        if not user_groups:
            logger.warning("     ⚠️ user_to_group 为空，跳过群组池初始化")
            return {"group_count": 0, "items": 0}

        group_ids = {value.decode() if isinstance(value, bytes) else str(value) for value in user_groups.values()}
        hot_articles = await sorted_set_get_range("rec_pool:hot:article", 0, 499, with_scores=True, desc=True)
        hot_videos = await sorted_set_get_range("rec_pool:hot:video", 0, 499, with_scores=True, desc=True)

        total_items = 0
        for group_id in group_ids:
            if hot_articles:
                await delete_key(f"rec_pool:group:{group_id}:article")
                await sorted_set_add(
                    f"rec_pool:group:{group_id}:article",
                    {str(item): float(score) for item, score in hot_articles},
                )
                total_items += len(hot_articles)
            if hot_videos:
                await delete_key(f"rec_pool:group:{group_id}:video")
                await sorted_set_add(
                    f"rec_pool:group:{group_id}:video",
                    {str(item): float(score) for item, score in hot_videos},
                )
                total_items += len(hot_videos)

        logger.info("     ✅ 群组池写入完成，共更新 %s 个群组", len(group_ids))
        return {"group_count": len(group_ids), "items": total_items}

    # ------------------------------------------------------------------
    # 相似度数据初始化
    # ------------------------------------------------------------------
    async def _initialize_similarity_data(self) -> dict[str, Any]:
        logger.info("🔗 正在基于真实标签/分类生成相似度数据 ...")

        article_features = await self._build_feature_map(
            await crud_article.get_published(self.db, limit=200),
            content_type="article",
        )
        video_features = await self._build_feature_map(
            await crud_video.get_multi(self.db, limit=200),
            content_type="video",
        )

        article_written = await self._write_similarity_pool("article", article_features)
        video_written = await self._write_similarity_pool("video", video_features)

        logger.info(
            "   ✅ 相似度写入完成 - 文章 %s 条, 视频 %s 条",
            article_written,
            video_written,
        )
        return {"articles": article_written, "videos": video_written}

    async def _build_feature_map(self, items: Iterable[Any], content_type: str) -> dict[int, set[str]]:
        features: dict[int, set[str]] = {}
        for item in items:
            feature_set: set[str] = set()
            tags = getattr(item, "tags", None)
            if tags:
                feature_set.update({tag.name for tag in tags if getattr(tag, "name", None)})
            category = getattr(item, "category", None)
            if category and getattr(category, "name", None):
                feature_set.add(f"category:{category.name}")
            author_id = getattr(item, "author_id", None)
            if author_id:
                feature_set.add(f"author:{author_id}")

            if feature_set:
                features[item.id] = feature_set
        if not features:
            logger.warning("   ⚠️ %s 未找到可用特征，跳过相似度构建", content_type)
        return features

    async def _write_similarity_pool(self, content_type: str, feature_map: dict[int, set[str]]) -> int:
        if not feature_map:
            return 0
        written = 0
        for item_id, base_features in feature_map.items():
            candidates: list[tuple[int, float]] = []
            for other_id, other_features in feature_map.items():
                if other_id == item_id:
                    continue
                score = self._jaccard(base_features, other_features)
                if score > 0:
                    candidates.append((other_id, round(score, 4)))
            candidates.sort(key=lambda x: x[1], reverse=True)
            top_candidates = candidates[:10]
            key = f"rec_pool:similar:{content_type}:{item_id}"
            await delete_key(key)
            if top_candidates:
                await sorted_set_add(key, {str(cid): score for cid, score in top_candidates})
                written += 1
        return written

    @staticmethod
    def _jaccard(a: set[str], b: set[str]) -> float:
        if not a or not b:
            return 0.0
        intersection = len(a & b)
        union = len(a | b)
        if union == 0:
            return 0.0
        return intersection / union

    # ------------------------------------------------------------------
    # 数据完整性校验
    # ------------------------------------------------------------------
    async def _validate_data_integrity(self) -> list[dict[str, Any]]:
        logger.info("🔍 正在验证关键 Redis 数据完整性 ...")
        checks: list[tuple[str, str, str]] = [
            ("用户群组映射", "hash", "user_to_group"),
            ("热门文章池", "zset", "rec_pool:hot:article"),
            ("热门视频池", "zset", "rec_pool:hot:video"),
            ("最新文章池", "zset", "rec_pool:latest:article"),
            ("最新视频池", "zset", "rec_pool:latest:video"),
        ]
        results: list[dict[str, Any]] = []
        for name, data_type, key in checks:
            try:
                if data_type == "hash":
                    entries = await hash_get_all(key)
                    count = len(entries) if entries else 0
                else:
                    entries = await sorted_set_get_range(key, 0, 10)
                    count = len(entries)
                status = "ok" if count > 0 else "empty"
                icon = "✅" if count > 0 else "⚠️"
                logger.info("   %s %s (%s 条)", icon, name, count)
                results.append({"name": name, "key": key, "count": count, "status": status})
            except Exception as exc:  # noqa: BLE001
                logger.error("   ❌ %s 校验失败: %s", name, exc)
                results.append({"name": name, "key": key, "count": 0, "status": "error"})
        return results

    # ------------------------------------------------------------------
    # 报告生成
    # ------------------------------------------------------------------
    async def _generate_fix_report(self) -> None:
        self.report["generated_at"] = datetime.utcnow().isoformat()
        REPORT_PATH.parent.mkdir(parents=True, exist_ok=True)
        with REPORT_PATH.open("w", encoding="utf-8") as fp:
            json.dump(self.report, fp, indent=2, ensure_ascii=False)
        logger.info("📄 修复报告已写入 %s", REPORT_PATH.relative_to(PROJECT_ROOT))

    # ------------------------------------------------------------------
    # 工具方法
    # ------------------------------------------------------------------
    @staticmethod
    def _read_source(relative_path: str) -> str:
        file_path = PROJECT_ROOT / relative_path
        if not file_path.exists():
            return ""
        try:
            return file_path.read_text(encoding="utf-8")
        except UnicodeDecodeError:
            return file_path.read_text(encoding="utf-8", errors="ignore")


# ----------------------------------------------------------------------
# CLI 入口
# ----------------------------------------------------------------------
async def main() -> None:
    parser = argparse.ArgumentParser(description="推荐系统修复脚本")
    parser.add_argument(
        "--stage",
        choices=["1", "2", "3", "all"],
        required=True,
        help="修复阶段: 1=阻断修复, 2=数据闭环, 3=校验报告, all=全部阶段",
    )
    args = parser.parse_args()

    logger.info("🚀 推荐系统修复脚本启动 (stage=%s)\n", args.stage)

    async with RecommendationSystemFixer() as fixer:
        if args.stage in {"1", "all"}:
            await fixer.fix_stage_1()
        if args.stage in {"2", "all"}:
            await fixer.fix_stage_2()
        if args.stage in {"3", "all"}:
            await fixer.fix_stage_3()

    logger.info("🎉 脚本执行完成")


if __name__ == "__main__":
    asyncio.run(main())
