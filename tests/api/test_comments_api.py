import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from unittest.mock import Async<PERSON>ock, MagicMock, ANY

from app.api.endpoints import comments
from app.models.comment import CommentType
from app.schemas.comment import CommentCreate


@pytest.mark.asyncio
async def test_create_scratch_comment_success(monkeypatch):
    comment_in = CommentCreate(
        content="测试评论",
        comment_type=CommentType.SCRATCH,
        article_id=None,
        video_id=None,
        scratch_id=123,
        parent_id=None,
    )

    current_user = MagicMock()
    current_user.id = 1
    db = AsyncMock()

    scratch_project = MagicMock()
    scratch_project.is_published = True
    monkeypatch.setattr(
        comments.crud.scratch_product,
        "get",
        AsyncMock(return_value=scratch_project),
    )
    created_comment = MagicMock(
        comment_type=CommentType.SCRATCH,
        article_id=None,
        video_id=None,
        scratch_id=123,
    )
    monkeypatch.setattr(
        comments.crud.comment,
        "create",
        AsyncMock(return_value=created_comment),
    )

    content_stats_service = MagicMock()
    content_stats_service.update_comment_count = AsyncMock()

    result = await comments.create_comment(
        db=db,
        comment_in=comment_in,
        current_user=current_user,
        article_cache_service=MagicMock(),
        video_cache_service=MagicMock(),
        content_stats_service=content_stats_service,
    )

    assert result is created_comment
    comments.crud.scratch_product.get.assert_awaited_once_with(db=db, id=123)
    comments.crud.comment.create.assert_awaited_once_with(
        db,
        obj_in=comment_in,
        author_id=current_user.id,
    )
    content_stats_service.update_comment_count.assert_awaited_once_with(
        content_type="scratch",
        content_id=123,
        increment=1,
    )


@pytest.mark.asyncio
async def test_get_scratch_comments_not_found(monkeypatch):
    monkeypatch.setattr(
        comments.crud.scratch_product,
        "get",
        AsyncMock(return_value=None),
    )

    with pytest.raises(HTTPException) as exc:
        await comments.get_scratch_comments(
            db=AsyncMock(),
            project_id=404,
            cursor=None,
            size=20,
            sort_by="like_count",
            flat=False,
            max_level=10,
            current_user=None,
            content_stats_service=MagicMock(),
        )

    assert exc.value.status_code == status.HTTP_404_NOT_FOUND
    comments.crud.scratch_product.get.assert_awaited_once_with(db=ANY, id=404)
