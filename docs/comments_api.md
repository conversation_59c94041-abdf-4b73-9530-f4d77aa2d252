# 评论模块 API 文档

面向前端的评论接口说明，涵盖创建、查询、管理与点赞相关端点，帮助快速接入文章、视频与 Scratch 内容的互动能力。

## 1. 基础信息
- **基础路径**：`/api/v1/comments`（主应用在 `app/main.py:174` 处挂载 `api_router`）。
- **内容类型**：`article` / `video` / `scratch`，枚举定义于 `app/models/comment.py:18`。
- **鉴权方式**：
  - 推荐使用 `Authorization: Bearer <token>`；
  - 或 Cookie 携带 `access_token=<token>`。
- **权限约束**：
  - 创建评论：`COMMENT:CREATE:OWN`（依赖 `require_permission`，见 `app/api/permission_deps.py:33`）。
  - 更新/删除：作者需分别具备 `COMMENT:UPDATE:OWN`、`COMMENT:DELETE:OWN`，或具备 `COMMENT:MANAGE:ALL`。
  - 点赞/查询个人评论：需登录用户上下文。
- **统一响应包装**：除 `204 No Content` 外，所有接口默认经过 `ResponseFormatterMiddleware` 包装，业务数据位于 `data` 字段。
- **速率限制**：继承全局限流设置（`app/core/limiter.py`），评论接口未单独覆盖，默认 60 次/分钟。

## 2. 数据模型

### 2.1 Comment 基础字段
| 字段 | 类型 | 说明 |
| -- | -- | -- |
| `id` | `integer` | 评论唯一 ID（嵌套模型别名为 `comment_id` / `reply_id`，见 `app/schemas/comment.py:46`、`app/schemas/comment.py:60`） |
| `content` | `string` | 评论正文，前端需做长度限制（数据库字段 `Text`，无硬性上限） |
| `comment_type` | `string` | 内容类型枚举值 |
| `article_id` / `video_id` / `scratch_id` | `integer?` | 根据 `comment_type` 对应的内容主键，至少填一个 |
| `parent_id` | `integer?` | 父评论 ID，存在时代表此条为回复 |
| `reply_to_id` | `integer?` | 真正回复的那条评论 ID（后端自动补全） |
| `author` | `User` | 作者信息，结构同 `app/schemas/user.py` |
| `is_visible` | `boolean` | 是否对前端可见 |
| `created_at` / `updated_at` | `string` | ISO-8601 时间戳 |
| `like_count` | `integer` | 点赞数量，来自统计服务 |
| `is_liked` | `boolean` | 当前用户是否点赞 |

### 2.2 扁平结构 `FlatComment`
- 定义：`app/schemas/comment.py:90`。
- 额外字段：`level`（层级，0 为顶层）、`path`（层级路径，例如 `12.45`）、`reply_count`（直接子回复数）、`total_reply_count`（包含所有层级）。

### 2.3 树形结构 `CommentWithReplies`
- 定义：`app/schemas/comment.py:76`。
- `replies` 字段为 `Reply` 列表，`reply_count` 表示直接子回复数量。

### 2.4 点赞状态 `LikeStatus`
- 定义：`app/schemas/like.py:45`。
- 字段：`content_type`, `content_id`, `is_liked`, `like_count`。

## 3. 接口详解

### 3.1 创建评论
- **HTTP**：`POST /api/v1/comments/`
- **权限**：`COMMENT:CREATE:OWN`
- **请求体**：`CommentCreate`（`app/schemas/comment.py:21`）
  ```json
  {
    "content": "期待后续更新！",
    "comment_type": "article",
    "article_id": 42,
    "parent_id": null
  }
  ```
- **主要校验**（见 `app/api/endpoints/comments.py:58`）：
  - 回复必须引用存在且同类型的父评论；
  - 目标内容需存在且已发布（文章与视频通过缓存服务检查，Scratch 通过 CRUD）；
  - 参数缺失或跨内容类型会返回 `400 Bad Request`。
- **典型响应**：返回 `Comment` 模型，包含作者信息与点赞统计。
- **副作用**：新增评论后会写入 outbox 通知并调用 `ContentStatsService.update_comment_count` 自增评论数。

### 3.2 获取文章评论
- **HTTP**：`GET /api/v1/comments/article/{article_id}`（`app/api/endpoints/comments.py:139`）
- **权限**：匿名可访问，文章需已审核发布。
- **查询参数**：
  | 参数 | 类型 | 默认 | 说明 |
  | -- | -- | -- | -- |
  | `cursor` | `string?` | `null` | 游标分页位置（`CursorPaginationParams`，见 `app/core/pagination.py:23`）|
  | `size` | `int` | `20` | 每页大小，最大 100 |
  | `sort_by` | `string` | `like_count` | 可选 `like_count`/`created_at` |
  | `flat` | `bool` | `true` | `true` 返回 `FlatCommentList`，`false` 返回 `CommentCursorList` |
  | `max_level` | `int` | `10` | 扁平化时保留的最大层级 |
- **点赞信息**：通过 `ContentStatsService.batch_get_stats` 批量回填。

### 3.3 获取视频评论
- **HTTP**：`GET /api/v1/comments/video/{video_id}`（`app/api/endpoints/comments.py:417`）
- **权限**：匿名可访问，视频需已发布。
- **默认结构**：树形（`flat=false`）。其余参数与文章一致。

### 3.4 获取 Scratch 评论
- **HTTP**：`GET /api/v1/comments/scratch/{project_id}`（`app/api/endpoints/comments.py:692`）
- **权限**：匿名可访问，项目需 `is_published=True`。
- **结构控制**：同文章、视频。

### 3.5 获取我的评论
- **HTTP**：`GET /api/v1/comments/user/me`（`app/api/endpoints/comments.py:921`）
- **权限**：登录用户。
- **查询参数**：`skip`（默认 0）、`limit`（默认 100）。返回普通列表按创建时间倒序。

### 3.6 更新评论
- **HTTP**：`PUT /api/v1/comments/{comment_id}`（`app/api/endpoints/comments.py:934`）
- **权限**：作者 + `COMMENT:UPDATE:OWN` 或后台具有 `COMMENT:MANAGE:ALL`。
- **请求体**：`CommentUpdate`，允许修改 `content`、`is_visible`。
- **限制**：仅可更新存在且可见的评论，越权返回 `403 Forbidden`。

### 3.7 删除评论
- **HTTP**：`DELETE /api/v1/comments/{comment_id}`（`app/api/endpoints/comments.py:973`）
- **权限**：作者 + `COMMENT:DELETE:OWN` 或后台具有 `COMMENT:MANAGE:ALL`。
- **响应**：`204 No Content`。
- **副作用**：调用 `ContentStatsService.update_comment_count` 处理评论数减一。

### 3.8 评论点赞切换
- **HTTP**：`POST /api/v1/comments/{comment_id}/like`（`app/api/endpoints/comments.py:1021`）
- **权限**：登录用户。
- **逻辑**：调用 `crud.like.toggle_like` 切换状态，并返回最新 `LikeStatus`。

### 3.9 查询评论点赞状态
- **HTTP**：`GET /api/v1/comments/{comment_id}/like`（`app/api/endpoints/comments.py:1062`）
- **权限**：登录用户。
- **用途**：页面初始化时获取当前用户点赞态，用于刷新 UI。

## 4. 常见错误码

| HTTP 状态 | 触发场景 | 参考代码位置 |
| -- | -- | -- |
| `400 Bad Request` | 排序字段非法、回复跨内容类型、缺失内容 ID 等 | `app/api/endpoints/comments.py:72`, `app/api/endpoints/comments.py:179` |
| `403 Forbidden` | 内容未发布或权限不足 | `app/api/endpoints/comments.py:168`, `app/api/endpoints/comments.py:956` |
| `404 Not Found` | 评论 / 内容不存在或被隐藏 | `app/api/endpoints/comments.py:64`, `app/api/endpoints/comments.py:1009` |
| `429 Too Many Requests` | 触发限流 | `app/core/limiter.py` |

## 5. 前端接入建议
- **游标分页**：首次请求不携带 `cursor`，翻页使用响应中的 `next_cursor` / `previous_cursor`。`cursor="0"` 被视为首屏，后端会忽略。
- **列表渲染**：
  - 扁平模式：利用 `level` 与 `path` 进行层级缩进，可按 `reply_count`/`total_reply_count` 展示回复概览。
  - 树形模式：递归遍历 `replies`，按 `created_at` 升序展示子节点。
- **回复交互**：提交回复时仅需提供 `parent_id`，后端会自动写入 `reply_to_id` 并回填 `reply_to_user` 信息。
- **点赞同步**：`POST` 返回的 `LikeStatus` 已含点赞总数及当前状态，可直接更新 UI，无需再次 `GET`。
- **内容过滤**：后端会过滤不可见评论，前端无需额外处理软删除态。
