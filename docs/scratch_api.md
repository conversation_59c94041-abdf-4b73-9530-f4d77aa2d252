# Scratch API 文档

## 概述
Scratch API 提供 Scratch 项目的创建、查询、更新、删除、改编及搜索等能力，供 Web 前端或第三方应用集成。接口基于 FastAPI 异步框架实现，所有数据通过 Pydantic 模型序列化，支持细粒度权限与游标分页。

### 基础信息
- **Base URL**：`/api/v1/scratch`
- **认证方式**：JWT，所有写操作需在请求头附加 `Authorization: Bearer <token>`；读取公开项目可匿名访问
- **权限体系**：通过 `require_permission` 注入，核心权限为 `Permissions.CONTENT_CREATE_OWN`、`CONTENT_UPDATE_OWN`、`CONTENT_DELETE_OWN`、`CONTENT_READ_PUBLIC`，资源类型为 `ResourceType.SCRATCH_PROJECT`
- **限流**：`GET /scratch/{project_id}` 默认限制为 `100/minute`
- **分页模型**：列表接口返回 `CursorPaginationResponse`，包含 `items`、`has_next`、`has_previous`、`next_cursor`、`previous_cursor`、`total_count`
- **错误处理**：标准 HTTP 状态码，例如 400（参数错误）、403（无权限）、404（资源不存在）、422（请求体验证失败）

### 认证流程概述
1. 用户通过 `/api/token` 或其他登录端点获取 JWT
2. 前端将 Token 缓存，并在需要鉴权的接口请求头加入 `Authorization: Bearer <token>`
3. 后端通过依赖自动解析 Token 注入 `current_user`
4. 对公开资源使用 `deps.get_current_user_optional`，匿名用户可读取

## 核心数据模型
- **ScratchProductCreate**：创建项目输入，字段 `title`（必填）、`cover_url`、`can_adapt`、`code`（dict 原样存储）、`author_id`（后端从会话注入）
- **ScratchProductUpdate**：详情更新输入，可按需覆盖 `title`、`description`、`cover_url`、`is_published`、`can_adapt`、`code`
- **ScratchProductAdapt**：改编输入，支持设置 `adaptation_type`（`remix`/`copy`/`original`）、是否继承资源和代码、`custom_notes`
- **ScratchProductOut**：项目对外输出，包含基础字段、作者信息、统计数据、改编链字段（`original_project`、`root_project`、`adaptation_info` 等）
- **CursorPaginationParams**：游标分页参数，字段 `cursor`、`size`（1-100，默认 20）、`order_by`（默认 `id`）、`order_direction`（`asc`/`desc`）

## 端点详情

### 1. 创建项目 — `POST /scratch/create`
- **描述**：创建原创、复制或重混项目；请求体直接传 Scratch JSON
- **权限**：`Permissions.CONTENT_CREATE_OWN`
- **查询参数**：
  - `title` (str, 必填)：新项目标题
  - `original_id` (int, 选填)：原项目 ID（复制/重混时必填）
  - `is_copy` (int, 选填)：传 `1` 表示复制，后端会继承原代码并设置 `AdaptationType.COPY`
  - `is_remix` (int, 选填)：传 `1` 表示重混，需原项目允许改编
- **请求体**：Scratch 项目 JSON（对象）
- **响应**：`200 OK`，`{"project_id": <int>}`
- **错误提示**：
  - `400`：复制/重混缺少 `original_id`
  - `403`：复制他人项目或原项目禁止改编
  - `404`：原项目不存在
  - `422`：请求体不是合法 JSON

### 2. 获取项目详情 — `GET /scratch/{project_id}`
- **描述**：获取项目完整信息；`format=raw` 时返回项目的原始 Scratch JSON（包裹在统一响应的 `data` 字段中）
- **权限**：公开项目匿名可读；私有项目需满足资源权限
- **路径参数**：`project_id` (int)
- **查询参数**：`format` (`raw` 返回原始代码 JSON；其他值返回 `ScratchProductOut`)
- **响应**：`200 OK`
- **注意**：接口被限流 `100/minute`
- **错误提示**：`404`：项目不存在

### 3. 更新标题与代码 — `PUT /scratch/{project_id}`
- **描述**：同时更新项目标题与代码
- **权限**：`Permissions.CONTENT_UPDATE_OWN`
- **查询参数**：`title` (str, 必填)
- **请求体**：Scratch 项目 JSON
- **响应**：`200 OK`，`{"project_id": <int>}`
- **已知问题**：当前权限系统在处理 `Scope.OWN` 时不会强制验证资源归属（参见 `PermissionChecker._do_check_permission`），导致只要角色拥有 `content:update:own` 或 `scratch_project:update:own` 权限，就能修改他人作品。临时缓解方案是在端点内手动校验 `project.author_id` 与 `current_user.id`；建议后续修复权限系统，在匹配 `Scope.OWN` 权限时统一检查资源所有者字段。

### 4. 更新项目详情 — `PUT /scratch/{project_id}/detail`
- **描述**：更新描述、封面、发布状态、是否可改编等信息
- **权限**：`Permissions.CONTENT_UPDATE_OWN`
- **请求体**：`ScratchProductUpdate`
- **响应**：`200 OK`，`{"project_id": <int>}`

### 5. 分享项目 — `POST /scratch/{project_id}/share`
- **描述**：将 Scratch 项目标记为已发布，允许他人访问和改编
- **权限**：`Permissions.CONTENT_UPDATE_OWN`（项目作者或具备相应管理权限）
- **请求头**：`Authorization: Bearer <token>`
- **请求体**：无需请求体
- **请求示例**：
  ```ts
  await fetch(`/api/v1/scratch/${projectId}/share`, {
    method: 'POST',
    headers: { Authorization: `Bearer ${token}` },
  });
  ```
- **成功响应**：`200 OK`，`ScratchPublishStatus`
  ```json
  {
    "project_id": 123,
    "is_published": true
  }
  ```
- **失败响应**：
  - `404 { "detail": "项目不存在" }`
- **服务端行为**：
  - 通过 `update_publish_status` 将项目 `is_published` 置为 `true` 并提交事务
  - 记录分享日志，用于审计

### 6. 取消分享 — `POST /scratch/{project_id}/unshare`
- **描述**：撤销项目分享，将 `is_published` 置为 `false`，不再对外公开
- **权限**：`Permissions.CONTENT_UPDATE_OWN`
- **请求头**：`Authorization: Bearer <token>`
- **请求体**：无需请求体
- **请求示例**：
  ```ts
  await fetch(`/api/v1/scratch/${projectId}/unshare`, {
    method: 'POST',
    headers: { Authorization: `Bearer ${token}` },
  });
  ```
- **成功响应**：`200 OK`，`ScratchPublishStatus`
  ```json
  {
    "project_id": 123,
    "is_published": false
  }
  ```
- **失败响应**：
  - `404 { "detail": "项目不存在" }`
- **服务端行为**：
  - 调用 `update_publish_status` 将项目状态改为未发布并刷新 ORM 对象
  - 记录取消分享日志

### 7. 上传项目封面 — `POST /scratch/{project_id}/cover/upload`
- **描述**：复用通用上传管线，将封面图写入阿里云 OSS 并自动更新项目 `cover_url`
- **权限**：`Permissions.CONTENT_UPDATE_OWN`（项目作者或具备相应管理权限）
- **请求头**：`Authorization: Bearer <token>`
- **请求体**：`multipart/form-data`
  - `file` (必填)：图片文件，支持 JPG/PNG/GIF；非 GIF 会转换为 WebP
  - 文件大小限制与 `/upload/file` 一致（默认 5GB）
- **请求示例**：
  ```ts
  const form = new FormData();
  form.append('file', fileInput.files[0]);
  await fetch(`/api/v1/scratch/${projectId}/cover/upload`, {
    method: 'POST',
    headers: { Authorization: `Bearer ${token}` },
    body: form,
  });
  ```
- **成功响应**：`200 OK`，`UploadResponse`
  ```json
  {
    "file_hash": "c52f5a9a0ed9...",
    "file_url": "/steam/images/c52f5a9a0ed9.webp",
    "duration": null,
    "width": null,
    "height": null
  }
  ```
- **失败响应**：
  - `404 { "detail": "项目不存在" }`
  - `413 { "detail": "File is too large. Limit is 5368709120 bytes." }`
  - `500 { "detail": "封面图上传失败" }`（OSS 处理失败）
- **服务端行为**：
  - `handle_single_image_upload` 完成去重、转码、FileHash 入库与缓存
  - 成功后立即更新 `scratch_products.cover_url` 并记录日志（包含 user_id、project_id、file_hash）

### 8. 删除项目 — `DELETE /scratch/{project_id}`
- **描述**：删除项目（若存在改编子项目会阻止删除）
- **权限**：`Permissions.CONTENT_DELETE_OWN`
- **响应**：`204 No Content`
- **错误提示**：`400`：存在改编子项目；`404`：项目不存在

### 9. 获取项目列表 — `GET /scratch/`
- **描述**：游标分页获取项目列表，支持筛选
- **查询参数**：
  - `cursor` (str, 选填)：上一页返回的 `next_cursor`
  - `size` (int, 选填)：单页数量（1-100，默认 20）
  - `order_by` (str, 选填)：排序字段（默认 `id`）
  - `order_direction` (`asc`/`desc`)
  - `difficulty_level` (int, 选填)：1-5
  - `category` (str, 选填)
  - `author_id` (int, 选填)
  - `adaptation_type` (enum, 选填)：`original` / `remix` / `copy`
  - `is_public` (bool, 选填，默认 `true`)：是否仅返回已分享（发布）项目
- **响应**：`200 OK`，`CursorPaginationResponse[ScratchProductOut]`
- **说明**：未显式指定 `is_public` 时默认返回已发布的项目；如需获取草稿，需显式传入 `is_public=false` 并确保具备权限

### 10. 批量获取项目 — `POST /scratch/multiple`
- **描述**：根据 ID 列表批量返回项目，可选择保持顺序和包含改编信息
- **权限**：公开访问（结果仍会经过权限过滤）
- **查询参数**：
  - `preserve_order` (bool, 默认 `true`)
  - `include_adaptations` (bool, 默认 `false`)
- **请求体**：`{"projectIds": [<int>, ...]}`（最多 100 个）
- **响应**：`200 OK`，`list[ScratchProductOut]`
- **错误提示**：`400`：ID 数量超过 100

### 11. 获取用户项目 — `GET /scratch/user/{user_id}/projects`
- **描述**：按作者分页获取项目，可选返回改编作品
- **权限**：公开；仅作者本人或管理员可查看私有项目
- **查询参数**：
  - `cursor`、`size`、`order_by`、`order_direction`
  - `include_adaptations` (bool, 默认 `false`)
- **响应**：`200 OK`，`CursorPaginationResponse[ScratchProductOut]`

### 12. 搜索项目 — `GET /scratch/search`
- **描述**：基于标题/描述关键词搜索，可组合筛选
- **权限**：公开访问
- **查询参数**：
  - `q` (str, 必填)：搜索关键词
  - `cursor`、`size`、`order_by`、`order_direction`
  - `difficulty_level`、`category`、`adaptation_type`
- **响应**：`200 OK`，`CursorPaginationResponse[ScratchProductOut]`

### 13. 获取改编历史 — `GET /scratch/{project_id}/adaptation-history`
- **描述**：返回改编活动记录（时间、改编者、类型等）
- **权限**：公开访问（仍受项目可见性限制）
- **查询参数**：`limit` (int, 默认 50，范围 1-200)
- **响应**：`200 OK`，`list[dict]`
- **注意**：后端返回结构可能根据业务扩展，前端需做兼容处理

## 前端接入指南

### 鉴权与错误处理
- 对写操作统一封装请求拦截器注入 Token
- 捕获 `403` 提示用户无权限或引导登录
- 对 `422` 解析返回的 `detail` 信息，提示用户修正提交内容

### 游标分页实践
```javascript
let cursor = null;
let loading = false;

async function loadMore() {
  if (loading) return;
  loading = true;
  const query = new URLSearchParams({ size: '20' });
  if (cursor) query.set('cursor', cursor);
  const res = await fetch(`/api/v1/scratch?${query.toString()}`);
  if (!res.ok) throw new Error('Failed to load projects');
  const { items, next_cursor, has_next } = await res.json();
  render(items);
  cursor = has_next ? next_cursor : null;
  loading = false;
}
```

### 改编链展示
- `adaptation_info` 包含 `adapt_level`、`adaptation_type`、`original_project_id`
- 可根据 `adaptation_type` 展示 “Remixed from …” 或 “Copied from …” 链接
- 若需整条链，可在客户端结合 `/adaptation-history` 构建时间线

### 批量与筛选
- 批量接口请求前使用 `Array.from(new Set(ids))` 去重，避免后端重复过滤
- 列表筛选条件组合时请同步 UI 展示，明确当前生效的 `size`、`order_by`

### 兼容建议
- `POST /scratch/multiple` 当前使用 `projectIds` 字段；后端共用的 `MultipleData` Pydantic 模型默认字段为 `articleIds`，若未来发生变更请关注接口改动通知
- `GET /scratch/{project_id}/adaptation-history` 返回结构依赖业务实现，前端需容忍缺失字段或额外属性

## 已知注意事项
1. 复制操作仅允许作者本人，重混需校验原项目 `can_adapt` 标记；前端操作前可先调用详情接口获取最新状态
2. `format=raw` 可用于仅加载代码数据，适合编辑器初始化；若需要完整数据请移除该参数
3. 鼓励统一封装分页公共逻辑，复用 `items` / `next_cursor` 字段，避免误用历史的 `data`/`limit` 命名
