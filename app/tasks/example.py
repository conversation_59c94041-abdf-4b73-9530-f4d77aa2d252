from app.core.celery import app


import base64
import logging

from app.config import get_settings
from app.services.partial_upload import PartialUpload

@app.task
def example_task(message: str) -> str:
    """
    示例任务，接收一个消息并返回处理后的消息。

    Args:
        message (str): 要处理的消息

    Returns:
        str: 处理后的消息
    """
    result = f"处理的消息: {message}"
    return result


logger = logging.getLogger(__name__)
settings = get_settings()
_partial_upload = PartialUpload()


@app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_jitter=True,
    max_retries=3,
)
def upload_backpack_object(self, data_b64: str, object_name: str) -> str:
    """Upload backpack object to OSS via Celery worker.

    Args:
        data_b64: Base64 representation of the file bytes.
        object_name: Full OSS object key to store the file at.

    Returns:
        str: Stored object path without leading slash.
    """
    try:
        file_bytes = base64.b64decode(data_b64)
    except Exception as exc:  # pragma: no cover - robustness guard
        logger.error("Failed to decode base64 payload for object %s: %s", object_name, exc)
        raise self.retry(exc=exc)

    try:
        if len(file_bytes) < settings.OSS_MULTIPART_THRESHOLD:
            file_url = _partial_upload._upload_small_file(file_bytes, object_name, 3)
        else:
            file_url = _partial_upload._upload_large_file(file_bytes, object_name, 3)

        if not file_url:
            raise ValueError("OSS upload returned empty path")

        return file_url[1:] if file_url.startswith("/") else file_url
    except Exception as exc:  # pragma: no cover - network operation
        logger.error("Failed to upload backpack object %s via Celery: %s", object_name, exc)
        raise self.retry(exc=exc)
