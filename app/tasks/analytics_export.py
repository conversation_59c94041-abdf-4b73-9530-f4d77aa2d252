"""分析导出任务"""

from app.core.celery import app
from app.core.logging import logger
from app.db.session import SessionLocal
from app.services.analytics.pipeline import AnalyticsPipeline


def get_pipeline() -> AnalyticsPipeline:
    return AnalyticsPipeline()


@app.task(name="app.tasks.analytics_export.generate")
async def task_generate_export(dataset: str, time_range: str = "last_7_days") -> dict:
    pipeline = get_pipeline()
    db = SessionLocal()
    try:
        result = await pipeline.export_snapshot(
            db,
            snapshot_type=dataset,
            time_range=time_range,
        )
        logger.info("Analytics export generated: %s", result.filename)
        return {"path": result.path, "rows": result.rows}
    finally:
        await db.close()
