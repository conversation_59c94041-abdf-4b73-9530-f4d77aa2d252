from typing import Any, Sequence

from sqlalchemy import and_, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload

from app.core.pagination import CursorPaginationParams, CursorPaginationResponse, CursorPaginator
from app.crud.base import CRUDBase
from app import models, schemas
from app.models.scratch import ScratchProduct
from app.schemas.scratch import (
    AdaptationType,
    ScratchProductAdapt,
    ScratchProductCreate,
    ScratchProductUpdate,
)


class CRUDScratchProduct(CRUDBase[ScratchProduct, ScratchProductCreate, ScratchProductUpdate]):
    async def get(
        self,
        db: AsyncSession,
        id: Any,
        *,
        options: Sequence[Any] | None = None,
    ) -> ScratchProduct | None:
        """获取单个Scratch项目，并预加载关联关系"""
        query = select(self.model).where(self.model.project_id == id)
        if options is None:
            options = [
                joinedload(self.model.author).joinedload(models.User.stats),
                joinedload(self.model.original_project)
                .joinedload(models.ScratchProduct.author)
                .joinedload(models.User.stats),
                joinedload(self.model.root_project)
                .joinedload(models.ScratchProduct.author)
                .joinedload(models.User.stats),
                joinedload(self.model.tags),
            ]
        if options:
            query = query.options(*options)
        result = await db.execute(query)
        return result.unique().scalar_one_or_none()

    async def get_code_only(
        self,
        db: AsyncSession,
        *,
        project_id: int,
    ) -> tuple[bool, dict | None]:
        """仅获取项目代码，供 format=raw 直接返回使用。"""
        result = await db.execute(
            select(self.model.code).where(self.model.project_id == project_id)
        )
        row = result.first()
        if row is None:
            return False, None
        return True, row[0]

    async def get_by_title(self, db: AsyncSession, *, title: str) -> ScratchProduct | None:
        """根据标题获取Scratch项目"""
        result = await db.execute(select(self.model).where(self.model.title == title))
        return result.scalar_one_or_none()

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[ScratchProduct]:
        """获取指定作者的Scratch项目列表"""
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .options(joinedload(self.model.author))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_published(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[ScratchProduct]:
        """获取已发布的Scratch项目列表"""
        result = await db.execute(
            select(self.model)
            .where(self.model.is_published.is_(True))
            .options(joinedload(self.model.author))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_multi_by_ids(self, db: AsyncSession, *, ids: list[int]) -> list[ScratchProduct]:
        """根据ID列表获取多个Scratch项目"""
        if not ids:
            return []
        query = (
            select(self.model)
            .where(self.model.project_id.in_(ids))
            .options(
                joinedload(self.model.author).joinedload(models.User.stats),
                joinedload(self.model.original_project)
                .joinedload(models.ScratchProduct.author)
                .joinedload(models.User.stats),
                joinedload(self.model.root_project)
                .joinedload(models.ScratchProduct.author)
                .joinedload(models.User.stats),
                joinedload(self.model.tags),
            )
        )
        result = await db.execute(query)
        return result.unique().scalars().all()

    async def get_related_by_category(
        self,
        db: AsyncSession,
        *,
        category: str | None,
        exclude_id: int,
        limit: int = 10,
    ) -> list[ScratchProduct]:
        """根据分类获取相关项目，若分类为空或不足则按热度补齐。"""

        def build_query() -> Any:
            return (
                select(self.model)
                .where(self.model.project_id != exclude_id)
                .where(self.model.is_published.is_(True))
                .order_by(self.model.visit_count.desc(), self.model.project_id.desc())
                .limit(limit)
                .options(
                    joinedload(self.model.author).joinedload(models.User.stats),
                    joinedload(self.model.tags),
                )
            )

        result_items: list[ScratchProduct] = []

        if category:
            category_query = build_query().where(self.model.category == category)
            result = await db.execute(category_query)
            result_items = result.unique().scalars().all()

        if len(result_items) < limit:
            fallback_query = build_query()
            result = await db.execute(fallback_query)
            fallback_items = result.unique().scalars().all()

            existing_ids = {item.project_id for item in result_items}
            for item in fallback_items:
                if item.project_id not in existing_ids:
                    result_items.append(item)
                if len(result_items) >= limit:
                    break

        return result_items

    async def get_paginated_projects(
        self,
        db: AsyncSession,
        *,
        params: CursorPaginationParams,
        filters: dict[str, Any] | None = None,
        current_user: models.User | None = None,
        include_adaptations: bool = False,
        include_total: bool = True,
    ) -> CursorPaginationResponse[schemas.ScratchProductOut]:
        """分页获取Scratch项目列表，支持多条件筛选。"""

        filters = filters or {}

        query = (
            select(self.model)
            .options(
                selectinload(self.model.author).selectinload(models.User.stats),
                selectinload(self.model.tags),
            )
        )

        if include_adaptations:
            query = query.options(
                selectinload(self.model.original_project)
                .selectinload(models.ScratchProduct.author)
                .selectinload(models.User.stats),
                selectinload(self.model.root_project)
                .selectinload(models.ScratchProduct.author)
                .selectinload(models.User.stats),
            )

        if (difficulty_level := filters.get("difficulty_level")) is not None:
            query = query.where(self.model.difficulty == str(difficulty_level))

        if category := filters.get("category"):
            query = query.where(self.model.category == category)

        if author_id := filters.get("author_id"):
            query = query.where(self.model.author_id == author_id)

        if adaptation_type := filters.get("adaptation_type"):
            type_value = (
                adaptation_type.value
                if isinstance(adaptation_type, AdaptationType)
                else str(adaptation_type)
            )
            query = query.where(self.model.adaptation_type == type_value)

        if "is_public" in filters:
            if filters["is_public"]:
                query = query.where(self.model.is_published.is_(True))
            else:
                query = query.where(self.model.is_published.is_(False))

        if "is_published" in filters:
            query = query.where(
                self.model.is_published.is_(bool(filters["is_published"]))
            )

        order_by_field_name = params.order_by
        if order_by_field_name in {"id", "project_id"}:
            cursor_field = self.model.project_id
        else:
            cursor_field = getattr(self.model, order_by_field_name, None)
            if cursor_field is None:
                cursor_field = self.model.project_id

        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=cursor_field,
            include_total=include_total,
        )

        items = [schemas.ScratchProductOut.model_validate(item) for item in paginated_result.items]

        return CursorPaginationResponse[schemas.ScratchProductOut](
            items=items,
            has_next=paginated_result.has_next,
            has_previous=paginated_result.has_previous,
            next_cursor=paginated_result.next_cursor,
            previous_cursor=paginated_result.previous_cursor,
            total_count=paginated_result.total_count,
        )

    async def get_paginated_liked_projects(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        params: CursorPaginationParams,
    ) -> CursorPaginationResponse:
        """获取用户点赞的 Scratch 项目列表（游标分页）。"""

        query = (
            select(self.model)
            .join(
                models.Like,
                and_(
                    models.Like.content_id == self.model.project_id,
                    models.Like.content_type == "scratch",
                ),
            )
            .where(
                models.Like.user_id == user_id,
                models.Like.is_active.is_(True),
                self.model.is_published.is_(True),
            )
        )

        if hasattr(self.model, "is_deleted"):
            query = query.where(self.model.is_deleted.is_(False))

        if params.order_by == "like_time":
            cursor_field = models.Like.created_at
        else:
            cursor_field = getattr(self.model, params.order_by, None)
            if cursor_field is None:
                cursor_field = self.model.project_id

        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=cursor_field,
            include_total=True,
        )

        return paginated_result

    async def get_paginated_favorite_projects(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        params: CursorPaginationParams,
    ) -> CursorPaginationResponse:
        """获取用户收藏的 Scratch 项目列表（游标分页）。"""

        query = (
            select(self.model)
            .join(
                models.Favorite,
                and_(
                    models.Favorite.content_id == self.model.project_id,
                    models.Favorite.content_type == "scratch",
                ),
            )
            .where(
                models.Favorite.user_id == user_id,
                models.Favorite.is_active.is_(True),
                self.model.is_published.is_(True),
            )
        )

        if hasattr(self.model, "is_deleted"):
            query = query.where(self.model.is_deleted.is_(False))

        if params.order_by == "favorite_time":
            cursor_field = models.Favorite.created_at
        else:
            cursor_field = getattr(self.model, params.order_by, None)
            if cursor_field is None:
                cursor_field = self.model.project_id

        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=cursor_field,
            include_total=True,
        )

        return paginated_result

    async def create(
        self, db: AsyncSession, *, obj_in: ScratchProductCreate, author_id: int, commit: bool = True
    ) -> ScratchProduct:
        """创建Scratch项目"""
        create_data = obj_in.model_dump()
        create_data["author_id"] = author_id

        # 对于原创项目，设置默认的改编相关字段
        create_data.setdefault("adapt_level", 0)
        create_data.setdefault("adaptation_type", AdaptationType.ORIGINAL.value)

        db_obj = self.model(**create_data)
        db.add(db_obj)
        if commit:
            await db.flush()
            self._create_event(
                db,
                topic="content.created",
                payload={"content_type": "scratch", "content_id": db_obj.project_id},
            )
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def create_adaptation(
        self,
        db: AsyncSession,
        *,
        original_project: ScratchProduct,
        obj_in: ScratchProductAdapt,
        author_id: int,
        commit: bool = True,
    ) -> ScratchProduct:
        """创建改编项目"""
        create_data = {
            "title": obj_in.title,
            "description": obj_in.description,
            "author_id": author_id,
            "original_project_id": original_project.project_id,
            "root_project_id": original_project.root_project_id or original_project.project_id,
            "adapt_level": original_project.adapt_level + 1,
            "adaptation_type": obj_in.adaptation_type.value,
            "can_adapt": True,  # 默认允许改编
            "is_published": False,  # 默认为草稿状态
        }

        # 如果需要继承封面图
        if obj_in.inherit_resources and original_project.cover_url:
            create_data["cover_url"] = original_project.cover_url

        # 如果需要继承代码
        if obj_in.inherit_code:
            create_data["code"] = original_project.code or {}

        db_obj = self.model(**create_data)
        db.add(db_obj)

        # 更新原项目的改编计数
        await self.increment_adapt_count(db, project_id=original_project.project_id, commit=False)

        if commit:
            await db.flush()
            self._create_event(
                db,
                topic="content.created",
                payload={"content_type": "scratch", "content_id": db_obj.project_id},
            )
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def update_publish_status(
        self, db: AsyncSession, *, db_obj: ScratchProduct, is_published: bool
    ) -> ScratchProduct:
        """更新项目发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_drafts(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[ScratchProduct]:
        """获取指定作者的草稿列表"""
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .where(self.model.is_published.is_(False))
            .options(joinedload(self.model.author))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_adaptations(
        self,
        db: AsyncSession,
        *,
        original_project_id: int,
        skip: int = 0,
        limit: int = 100,
        adapt_level: int | None = None,
        adaptation_type: AdaptationType | None = None,
    ) -> list[ScratchProduct]:
        """获取指定项目的改编列表"""
        query = select(self.model).where(self.model.original_project_id == original_project_id)

        if adapt_level is not None:
            query = query.where(self.model.adapt_level == adapt_level)

        if adaptation_type is not None:
            query = query.where(self.model.adaptation_type == adaptation_type.value)

        query = query.options(joinedload(self.model.author)).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def get_all_adaptations(
        self,
        db: AsyncSession,
        *,
        root_project_id: int,
        skip: int = 0,
        limit: int = 100,
    ) -> list[ScratchProduct]:
        """获取改编链中的所有项目"""
        query = (
            select(self.model)
            .where(self.model.root_project_id == root_project_id)
            .options(joinedload(self.model.author))
            .order_by(self.model.adapt_level.asc(), self.model.created_at.asc())
            .offset(skip)
            .limit(limit)
        )

        result = await db.execute(query)
        return result.scalars().all()

    async def get_adaptation_chain(
        self, db: AsyncSession, *, project_id: int
    ) -> list[ScratchProduct]:
        """获取从根项目到指定项目的完整改编链"""
        # 首先获取当前项目
        current_project = await self.get(db, id=project_id)
        if not current_project:
            return []

        # 如果是原创项目，直接返回
        if current_project.adapt_level == 0:
            return [current_project]

        # 构建改编链
        chain = []
        current = current_project

        # 向上追溯到根项目
        while current and current.original_project_id:
            chain.append(current)
            current = await self.get(db, id=current.original_project_id)

        # 添加根项目
        if current:
            chain.append(current)

        # 反转链表，从根项目到当前项目
        return list(reversed(chain))

    async def check_circular_reference(
        self, db: AsyncSession, *, original_project_id: int, new_author_id: int
    ) -> bool:
        """检查是否会创建循环引用"""
        # 获取原项目的改编链
        chain = await self.get_adaptation_chain(db, project_id=original_project_id)

        # 检查新作者是否已经在改编链中有项目
        for project in chain:
            if project.author_id == new_author_id:
                return True

        return False

    async def increment_adapt_count(
        self, db: AsyncSession, *, project_id: int, commit: bool = True
    ) -> None:
        """增加项目的改编计数"""
        stmt = (
            update(self.model)
            .where(self.model.project_id == project_id)
            .values(adapt_count=self.model.adapt_count + 1)
        )
        await db.execute(stmt)
        if commit:
            await db.commit()

    async def increment_like_count(
        self, db: AsyncSession, *, project_id: int, commit: bool = True
    ) -> None:
        """增加项目的点赞计数"""
        stmt = (
            update(self.model)
            .where(self.model.project_id == project_id)
            .values(like_count=self.model.like_count + 1)
        )
        await db.execute(stmt)
        if commit:
            await db.commit()

    async def decrement_like_count(
        self, db: AsyncSession, *, project_id: int, commit: bool = True
    ) -> None:
        """减少项目的点赞计数"""
        stmt = (
            update(self.model)
            .where(self.model.project_id == project_id)
            .values(like_count=self.model.like_count - 1)
        )
        await db.execute(stmt)
        if commit:
            await db.commit()

    async def increment_favorite_count(
        self, db: AsyncSession, *, project_id: int, commit: bool = True
    ) -> None:
        """增加项目的收藏计数"""
        stmt = (
            update(self.model)
            .where(self.model.project_id == project_id)
            .values(favorite_count=self.model.favorite_count + 1)
        )
        await db.execute(stmt)
        if commit:
            await db.commit()

    async def decrement_favorite_count(
        self, db: AsyncSession, *, project_id: int, commit: bool = True
    ) -> None:
        """减少项目的收藏计数"""
        stmt = (
            update(self.model)
            .where(self.model.project_id == project_id)
            .values(favorite_count=self.model.favorite_count - 1)
        )
        await db.execute(stmt)
        if commit:
            await db.commit()

    async def increment_visit_count(
        self, db: AsyncSession, *, project_id: int, commit: bool = True
    ) -> None:
        """增加项目的访问计数"""
        stmt = (
            update(self.model)
            .where(self.model.project_id == project_id)
            .values(visit_count=self.model.visit_count + 1)
        )
        await db.execute(stmt)
        if commit:
            await db.commit()

    async def bulk_update_stats(
        self, db: AsyncSession, stats_data: dict[int, dict[str, int]]
    ) -> None:
        """批量更新项目的统计数据"""
        if not stats_data:
            return

        update_statements = []
        for project_id, metrics in stats_data.items():
            allowed_metrics = {
                "visit_count": metrics.get("visit_count"),
                "adapt_count": metrics.get("adapt_count"),
                "favorite_count": metrics.get("favorite_count"),
            }
            # 移除值为 None 的项
            update_values = {k: v for k, v in allowed_metrics.items() if v is not None}

            if update_values:
                stmt = (
                    update(self.model)
                    .where(self.model.project_id == project_id)
                    .values(**update_values)
                )
                update_statements.append(stmt)

        # 一次性执行所有更新
        for stmt in update_statements:
            await db.execute(stmt)

        await db.commit()

    async def get_adaptation_statistics(
        self, db: AsyncSession, *, project_id: int
    ) -> dict[str, Any]:
        """获取项目的改编统计信息"""
        # 获取直接改编数量
        direct_adaptations = await db.execute(
            select(func.count(self.model.project_id)).where(
                self.model.original_project_id == project_id
            )
        )
        direct_count = direct_adaptations.scalar() or 0

        # 获取总改编数量（包括间接改编）
        total_adaptations = await db.execute(
            select(func.count(self.model.project_id)).where(
                self.model.root_project_id == project_id
            )
        )
        total_count = total_adaptations.scalar() or 0

        # 获取各层级的改编数量
        level_stats = await db.execute(
            select(self.model.adapt_level, func.count(self.model.project_id))
            .where(self.model.root_project_id == project_id)
            .group_by(self.model.adapt_level)
        )
        level_distribution = dict(level_stats.fetchall())

        return {
            "direct_adaptations": direct_count,
            "total_adaptations": total_count,
            "level_distribution": level_distribution,
            "max_level": max(level_distribution.keys()) if level_distribution else 0,
        }

    async def search_projects(
        self,
        db: AsyncSession,
        *,
        query: str,
        skip: int = 0,
        limit: int = 100,
        published_only: bool = True,
    ) -> list[ScratchProduct]:
        """搜索Scratch项目"""
        search_query = select(self.model).where(
            or_(
                self.model.title.ilike(f"%{query}%"),
                self.model.description.ilike(f"%{query}%"),
            )
        )

        if published_only:
            search_query = search_query.where(self.model.is_published.is_(True))

        search_query = search_query.options(joinedload(self.model.author)).offset(skip).limit(limit)

        result = await db.execute(search_query)
        return result.scalars().all()

    async def get_paginated_history_projects(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        params: CursorPaginationParams,
    ) -> CursorPaginationResponse:
        """
        获取用户浏览历史的Scratch项目分页列表
        - 使用 JOIN 查询直接在数据库层面进行分页
        - 支持按浏览时间排序
        """
        from sqlalchemy import and_

        from app.core.pagination import CursorPaginator
        from app.models.history import History

        query = (
            select(self.model)
            .join(
                History,
                and_(
                    self.model.project_id == History.content_id,
                    History.content_type == "scratch",
                ),
            )
            .options(joinedload(self.model.author))
        )

        filters = [
            History.user_id == user_id,
            self.model.is_published.is_(True),
        ]
        if hasattr(self.model, "is_deleted"):
            filters.append(self.model.is_deleted.is_(False))

        query = query.where(*filters)

        # --- 排序逻辑 ---
        # 默认按项目ID排序，如果指定了 history_time，则按浏览时间排序
        if params.order_by == "history_time":
            cursor_field = History.updated_at
        else:
            cursor_field = getattr(self.model, params.order_by, self.model.project_id)

        # 使用游标分页器
        return await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=cursor_field,
            include_total=True,
        )


# 创建实例
scratch_product = CRUDScratchProduct(ScratchProduct)
