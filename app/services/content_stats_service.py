"""
重构后的内容统计服务
"""

import asyncio
from typing import Any, Literal

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.config import settings
from app.core.logging import logger
from app.db.redis import (
    delete_key,
    execute_pipeline,
    increment_key_by,
    set_key,
)
from app.schemas.post import PostStats

ContentType = Literal["article", "video", "comment", "scratch", "post"]


class ContentStatsService:
    """
    一个统一的、高性能的内容统计服务。
    通过服务层与Redis交互，并提供原子化的更新接口。
    """

    def __init__(self):
        self.cache_expire_seconds = settings.CONTENT_STATS_CACHE_EXPIRE_SECONDS

    # --- Key Generation Methods ---

    def _get_key(self, content_type: ContentType, content_id: int, metric: str) -> str:
        """通用键生成器"""
        return f"{content_type}:{content_id}:{metric}"

    def _like_count_key(self, content_type: ContentType, content_id: int) -> str:
        return self._get_key(content_type, content_id, "like_count")

    def _likers_set_key(self, content_type: ContentType, content_id: int) -> str:
        return self._get_key(content_type, content_id, "likers")

    def _favorite_count_key(self, content_type: ContentType, content_id: int) -> str:
        return self._get_key(content_type, content_id, "favorite_count")

    def _favoriters_set_key(self, content_type: ContentType, content_id: int) -> str:
        return self._get_key(content_type, content_id, "favoriters")

    def _visit_count_key(self, content_type: ContentType, content_id: int) -> str:
        return self._get_key(content_type, content_id, "visit_count")

    def _comment_count_key(self, content_type: ContentType, content_id: int) -> str:
        return self._get_key(content_type, content_id, "comment_count")

    # --- Method Stubs for Implementation ---

    async def get_stats(
        self,
        db: AsyncSession,
        content_type: ContentType,
        content_id: int,
        user_id: int | None = None,
    ) -> dict[str, Any]:
        """获取单个内容的完整统计信息。"""
        stats_dict = await self.batch_get_stats(db, [(content_type, content_id)], user_id)
        return stats_dict.get((content_type, content_id), {})

    async def batch_get_stats(
        self,
        db: AsyncSession,
        content_items: list[tuple[ContentType, int]],
        user_id: int | None = None,
    ) -> dict[tuple[ContentType, int], dict[str, Any]]:
        """
        批量获取多个内容的统计信息，并处理缓存回填。
        """
        if not content_items:
            return {}

        commands = []
        for content_type, content_id in content_items:
            commands.extend(
                [
                    ("get", self._like_count_key(content_type, content_id)),
                    ("get", self._favorite_count_key(content_type, content_id)),
                    ("get", self._visit_count_key(content_type, content_id)),
                    ("get", self._comment_count_key(content_type, content_id)),
                ]
            )
            if user_id:
                commands.extend(
                    [
                        (
                            "sismember",
                            self._likers_set_key(content_type, content_id),
                            user_id,
                        ),
                        (
                            "sismember",
                            self._favoriters_set_key(content_type, content_id),
                            user_id,
                        ),
                    ]
                )

        try:
            results = await execute_pipeline(commands, write=False)
        except Exception as e:
            logger.error(f"批量获取统计信息时 Redis 操作失败: {e}")
            results = []
            missing_items = content_items
            stats = {item: {} for item in content_items}

        if not results:
            pass
        else:
            stats = {}
            missing_items = []
        step = 6 if user_id else 4

        for i, item_key in enumerate(content_items):
            chunk = results[i * step : (i + 1) * step]
            like_count, favorite_count, visit_count, comment_count = (
                int(c) if c is not None else None for c in chunk[:4]
            )

            if (
                like_count is None
                or favorite_count is None
                or visit_count is None
                or comment_count is None
            ):
                missing_items.append(item_key)
                stats[item_key] = {}
                continue

            is_liked = bool(chunk[4]) if user_id and len(chunk) > 4 else False
            is_favorited = bool(chunk[5]) if user_id and len(chunk) > 5 else False

            stats[item_key] = {
                "like_count": like_count,
                "favorite_count": favorite_count,
                "visit_count": visit_count,
                "comment_count": comment_count,
                "is_liked_by_user": is_liked,
                "is_favorited_by_user": is_favorited,
            }

        if missing_items:
            logger.info(f"缓存未命中，回填 {len(missing_items)} 个内容的统计信息")
            db_stats = await self._backfill_from_db(db, missing_items, user_id)
            stats.update(db_stats)

        return stats

    async def get_posts_stats(
        self,
        db: AsyncSession,
        *,
        post_ids: list[int],
        user_id: int | None = None,
    ) -> list[PostStats]:
        """批量获取沸点的统计信息，并转换为 PostStats 列表。"""
        if not post_ids:
            return []

        stats_dict = await self.batch_get_stats(
            db, [("post", post_id) for post_id in post_ids], user_id
        )

        results: list[PostStats] = []
        for post_id in post_ids:
            data = stats_dict.get(("post", post_id), {})
            like_count = int(data.get("like_count", 0) or 0)
            comment_count = int(data.get("comment_count", 0) or 0)
            repost_count = int(data.get("repost_count", 0) or 0)
            view_count = int(data.get("visit_count", 0) or 0)
            is_liked_by_user = bool(data.get("is_liked_by_user", False))
            is_reposted_by_user = bool(data.get("is_reposted_by_user", False))
            is_followed_author = bool(data.get("is_followed_author", False))

            results.append(
                PostStats(
                    content_id=post_id,
                    like_count=like_count,
                    comment_count=comment_count,
                    repost_count=repost_count,
                    view_count=view_count,
                    is_liked_by_user=is_liked_by_user,
                    is_reposted_by_user=is_reposted_by_user,
                    is_followed_author=is_followed_author,
                )
            )

        return results

    async def _backfill_from_db(
        self,
        db: AsyncSession,
        content_items: list[tuple[ContentType, int]],
        user_id: int | None,
    ) -> dict[tuple[ContentType, int], dict[str, Any]]:
        """从数据库获取统计信息并回填到缓存中，包含分布式锁以防止惊群效应。"""
        backfilled_stats = {}
        for content_type, content_id in content_items:
            lock_key = f"lock:backfill:{content_type}:{content_id}"

            if await set_key(lock_key, "1", expire=10):
                try:
                    logger.info(f"成功获取锁，开始为 {content_type}:{content_id} 回填缓存")
                    item_key = (content_type, content_id)
                    # 处理不同内容类型的统计查询
                    if content_type == "scratch":
                        # Scratch项目使用专门的service
                        from app.services.scratch_stats_service import ScratchStatsService

                        scratch_service = ScratchStatsService()
                        like_stats = await scratch_service._get_like_stats_from_db(
                            db, [content_id], user_id
                        )
                        favorite_stats = await scratch_service._get_favorite_stats_from_db(
                            db, [content_id], user_id
                        )
                        project = await crud.scratch_product.get(db=db, id=content_id)
                        visit_stats = {item_key: project.visit_count if project else 0}
                        comment_stats = await crud.comment.get_comment_counts_batch(
                            db=db, content_items=[item_key]
                        )
                    else:
                        like_stats_task = crud.like.get_content_likes_batch(
                            db=db, content_items=[item_key], user_id=user_id
                        )
                        favorite_stats_task = crud.favorite.get_content_favorites_batch(
                            db=db, content_items=[item_key], user_id=user_id
                        )
                        visit_stats_task = crud.video.get_visit_counts_batch(
                            db=db, content_items=[item_key]
                        )
                        (
                            like_stats,
                            favorite_stats,
                            visit_stats,
                        ) = await asyncio.gather(
                            like_stats_task,
                            favorite_stats_task,
                            visit_stats_task,
                        )
                        comment_stats = {(content_type, content_id): 0}
                    l_stats_raw = like_stats.get(item_key, {})
                    f_stats_raw = favorite_stats.get(item_key, {})
                    v_count_raw = visit_stats.get(item_key)
                    c_count_raw = comment_stats.get(item_key)
                    l_count = l_stats_raw.get("like_count", 0) or 0
                    is_liked = l_stats_raw.get("is_liked", False)
                    f_count = f_stats_raw.get("favorite_count", 0) or 0
                    is_favorited = f_stats_raw.get("is_favorited", False)
                    v_count = v_count_raw or 0
                    c_count = c_count_raw or 0
                    final_item_stats = {
                        "like_count": l_count,
                        "favorite_count": f_count,
                        "visit_count": v_count,
                        "comment_count": c_count,
                        "is_liked_by_user": is_liked,
                        "is_favorited_by_user": is_favorited,
                    }
                    backfilled_stats[item_key] = final_item_stats

                    # 回填缓存
                    backfill_commands = [
                        (
                            "set",
                            self._like_count_key(content_type, content_id),
                            l_count,
                        ),
                        (
                            "set",
                            self._favorite_count_key(content_type, content_id),
                            f_count,
                        ),
                        (
                            "set",
                            self._visit_count_key(content_type, content_id),
                            v_count,
                        ),
                        (
                            "set",
                            self._comment_count_key(content_type, content_id),
                            c_count,
                        ),
                        (
                            "expire",
                            self._like_count_key(content_type, content_id),
                            self.cache_expire_seconds,
                        ),
                        (
                            "expire",
                            self._favorite_count_key(content_type, content_id),
                            self.cache_expire_seconds,
                        ),
                        (
                            "expire",
                            self._visit_count_key(content_type, content_id),
                            self.cache_expire_seconds,
                        ),
                        (
                            "expire",
                            self._comment_count_key(content_type, content_id),
                            self.cache_expire_seconds,
                        ),
                    ]
                    await execute_pipeline(backfill_commands, write=True)
                finally:
                    await delete_key(lock_key)
            else:
                logger.info(f"未能获取锁 {content_type}:{content_id}，等待后重试")
                await asyncio.sleep(0.1)
                stats = await self.batch_get_stats(db, [(content_type, content_id)], user_id)
                backfilled_stats.update(stats)

        return backfilled_stats

    async def update_like_status(
        self, content_type: ContentType, content_id: int, user_id: int, is_liked: bool
    ) -> None:
        """原子化地更新点赞状态和计数。"""
        likers_key = self._likers_set_key(content_type, content_id)
        count_key = self._like_count_key(content_type, content_id)

        if is_liked:
            commands = [("sadd", likers_key, user_id), ("incr", count_key)]
        else:
            commands = [("srem", likers_key, user_id), ("decr", count_key)]
        try:
            await execute_pipeline(commands, write=True)
        except Exception as e:
            logger.error(f"更新点赞状态时 Redis 操作失败: {e}")

    async def update_favorite_status(
        self, content_type: ContentType, content_id: int, user_id: int, is_favorited: bool
    ) -> None:
        """原子化地更新收藏状态和计数。"""
        favoriters_key = self._favoriters_set_key(content_type, content_id)
        count_key = self._favorite_count_key(content_type, content_id)

        if is_favorited:
            commands = [("sadd", favoriters_key, user_id), ("incr", count_key)]
        else:
            commands = [("srem", favoriters_key, user_id), ("decr", count_key)]
        try:
            await execute_pipeline(commands, write=True)
        except Exception as e:
            logger.error(f"更新收藏状态时 Redis 操作失败: {e}")

    async def increment_visit_count(self, content_type: ContentType, content_id: int) -> None:
        """原子化地增加访问次数。"""
        key = self._visit_count_key(content_type, content_id)
        try:
            await increment_key_by(key, 1, expire=self.cache_expire_seconds)
        except Exception as e:
            logger.error(f"增加访问次数时 Redis 操作失败: {e}")

    async def update_comment_count(
        self, content_type: ContentType, content_id: int, increment: int = 1
    ) -> None:
        """原子化地更新评论计数。"""
        key = self._comment_count_key(content_type, content_id)
        try:
            await increment_key_by(key, increment, expire=self.cache_expire_seconds)
        except Exception as e:
            logger.error(f"更新评论计数时 Redis 操作失败: {e}")


# 全局实例将在 service_factory 中创建和管理
