"""
视频数据聚合服务
- 负责将视频的基础信息、作者、统计数据、审核信息等高效地聚合在一起。
"""

import asyncio

from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.core.logging import logger
from app.core.pagination import CursorPaginationResponse
from app.schemas.video import ContentStats as VideoContentStats
from app.services.content_stats_service import ContentStatsService
from app.services.user_cache_service import UserCacheService
from app.services.video_cache_service import VideoCacheService


class VideoAggregationService:
    def __init__(
        self,
        video_cache_service: VideoCacheService,
        content_stats_service: ContentStatsService,
        user_cache_service: UserCacheService,
    ):
        self.video_cache_service = video_cache_service
        self.content_stats_service = content_stats_service
        self.user_cache_service = user_cache_service

    """
    视频数据聚合服务
    """

    async def _aggregate_videos_by_ids(
        self,
        db: AsyncSession,
        *,
        video_ids: list[int],
        current_user: models.User | None = None,
        include_review: bool = False,
    ) -> list[schemas.VideoOut]:
        """
        根据视频ID列表，聚合完整的视频信息
        这是一个核心的私有方法，用于代码复用
        """
        logger.info(f"DEBUG: 开始聚合视频信息，输入视频ID: {video_ids}")

        if not video_ids:
            logger.warning("DEBUG: 视频ID列表为空，返回空列表")
            return []

        # 2. 批量获取视频基础数据
        logger.info("DEBUG: 调用缓存服务获取视频基础数据")
        videos_data = await self.video_cache_service.get_entities_batch(db, entity_ids=video_ids)

        logger.info(f"DEBUG: 从缓存服务返回的视频数据: {list(videos_data.keys())}")
        logger.info(
            f"DEBUG: 缓存服务返回的视频详情: {[(k, f'published:{v.is_published},approved:{v.is_approved}' if v else None) for k, v in videos_data.items()]}"
        )

        # 3. 收集所有需要关联查询的ID
        author_ids = {v.author_id for v in videos_data.values() if v and v.author_id}
        folder_ids = {v.folder_id for v in videos_data.values() if v and v.folder_id}
        logger.info(f"DEBUG: 收集到的作者ID: {author_ids}")
        logger.info(f"DEBUG: 收集到的文件夹ID: {folder_ids}")

        # 4. 构建并行任务列表
        tasks = {
            "stats": self.content_stats_service.batch_get_stats(
                db,
                content_items=[("video", vid) for vid in video_ids],
                user_id=current_user.id if current_user else None,
            ),
            "authors": self.user_cache_service.get_entities_batch(db, entity_ids=list(author_ids)),
            "folders": crud.video_folder.get_multi_by_ids(db, ids=list(folder_ids)),
        }
        if include_review:
            tasks["reviews"] = crud.review.get_multi_by_content_ids(
                db, content_type="video", content_ids=video_ids
            )

        logger.info(f"DEBUG: 构建的并行任务: {list(tasks.keys())}")

        # 5. 并行执行所有任务
        results = await asyncio.gather(*tasks.values())
        logger.info(f"DEBUG: 并行任务执行完成，结果数量: {len(results)}")

        # 6. 将结果列表转换为ID为键的字典以便快速查找
        stats_map = results[0]
        authors_map = results[1]  # get_users_batch returns a dict
        folders_map = {folder.id: folder for folder in results[2]}
        reviews_map = {}
        if include_review:
            reviews_map = {review.content_id: review for review in results[3]}

        if include_review:
            logger.info(f"DEBUG: 审核数据映射: {list(reviews_map.keys())}")

        # 7. 在内存中高效组装成 VideoOut 对象
        video_out_list = []
        for video_id in video_ids:
            video_base = videos_data.get(video_id)
            if not video_base:
                logger.warning(f"DEBUG: 视频ID {video_id} 在缓存数据中不存在，跳过")
                continue

            stats = stats_map.get(("video", video_id), {})
            author = authors_map.get(video_base.author_id)
            folder = folders_map.get(video_base.folder_id)
            review = reviews_map.get(video_id)
            keywords = [tag.name for tag in video_base.tags] if video_base.tags else []
            meta = schemas.ContentMeta(
                content_type="video",
                content_id=video_base.id,
                slug=video_base.slug,
                seo_title=video_base.title,
                seo_description=video_base.description,
                keywords=keywords,
            )

            # 创建基础数据字典
            video_data = video_base.model_dump()
            
            # 正确处理 stats：如果已经是 ContentStats 实例，则直接使用；否则从字典创建
            stats_obj = None
            if stats:
                if isinstance(stats, VideoContentStats):
                    stats_obj = stats
                else:
                    stats_obj = VideoContentStats(**stats)
            
            video_data.update(
                {
                    "stats": stats_obj,
                    "author": schemas.UserAggregated.from_orm(author) if author else None,
                    "review": schemas.ReviewBase.from_orm(review) if review else None,
                    "folder": schemas.VideoFolderOut.from_orm(folder) if folder else None,
                    "meta": meta,
                }
            )

            # 禁用验证器以避免meta字段被覆盖
            video_out = schemas.VideoOut.model_construct(**video_data)
            video_out_list.append(video_out)
            logger.info(f"DEBUG: 成功创建视频输出对象: {video_out.id} - {video_out.title}")

        logger.info(f"DEBUG: 聚合完成，最终视频列表长度: {len(video_out_list)}")
        return video_out_list

    async def get_aggregated_videos_by_ids(
        self,
        db: AsyncSession,
        *,
        video_ids: list[int],
        current_user: models.User | None = None,
        include_review: bool = False,
    ) -> list[schemas.VideoOut]:
        """
        获取按ID列表聚合的视频列表
        """
        if not video_ids:
            return []
        return await self._aggregate_videos_by_ids(
            db,
            video_ids=video_ids,
            current_user=current_user,
            include_review=include_review,
        )

    async def get_aggregated_video(
        self,
        db: AsyncSession,
        *,
        video_id: int,
        current_user: models.User | None = None,
    ) -> schemas.VideoOut | None:
        """
        获取单个的、完整聚合的视频信息

        :param db: 数据库会话
        :param video_id: 视频ID
        :param current_user: 当前登录用户
        :return: 完整的视频输出对象或None
        """
        # 1. 获取视频基础信息
        video_base = await self.video_cache_service.get_entity(db, entity_id=video_id)
        if not video_base:
            return None

        # 2. 先获取必需的平行数据
        stats_task = self.content_stats_service.get_stats(
            db,
            content_type="video",
            content_id=video_id,
            user_id=current_user.id if current_user else None,
        )
        author_task = self.user_cache_service.get_entity(db, entity_id=video_base.author_id)

        # 获取必需的数据
        stats, author = await asyncio.gather(stats_task, author_task)

        # 条件性获取可选数据
        folder = None
        if video_base.folder_id:
            folder = await crud.video_folder.get(db, id=video_base.folder_id)

        # TODO: 权限判断
        review = None
        is_owner = current_user and current_user.id == video_base.author_id
        if is_owner:
            review = await crud.review.get_by_content(db, content_type="video", content_id=video_id)

        # 3. 组装成 VideoOut 对象
        keywords = [tag.name for tag in video_base.tags] if video_base.tags else []
        meta = schemas.ContentMeta(
            content_type="video",
            content_id=video_base.id,
            slug=video_base.slug,
            seo_title=video_base.title,  # 默认使用标题
            seo_description=video_base.description,  # 默认使用描述
            keywords=keywords,  # 假设 tags 是关键词列表
        )

        # 创建基础数据字典
        video_data = video_base.model_dump()
        
        # 正确处理 stats：如果已经是 ContentStats 实例，则直接使用；否则从字典创建
        stats_obj = None
        if stats:
            if isinstance(stats, VideoContentStats):
                stats_obj = stats
            else:
                stats_obj = VideoContentStats(**stats)
        
        video_data.update(
            {
                "stats": stats_obj,
                "author": schemas.UserAggregated.from_orm(author) if author else None,
                "review": schemas.ReviewBase.from_orm(review) if review else None,
                "folder": schemas.VideoFolderOut.from_orm(folder) if folder else None,
                "meta": meta,
            }
        )

        # 禁用验证器以避免meta字段被覆盖
        video_out = schemas.VideoOut.model_construct(**video_data)

        return video_out

    async def get_videos_by_folder(
        self,
        db: AsyncSession,
        *,
        folder_id: int,
        cursor: int = 0,
        size: int = 12,
        status: str | None = None,
        current_user: models.User | None = None,
    ) -> CursorPaginationResponse[schemas.VideoOut]:
        """
        获取指定文件夹下的视频列表

        :param db: 数据库会话
        :param folder_id: 文件夹ID
        :param cursor: 游标位置，用于分页
        :param size: 每页大小
        :param status: 视频状态过滤 (draft, published_pending, published_rejected, published_approved)
        :param current_user: 当前登录用户
        :return: 分页的视频列表
        """

        from app.core.pagination import CursorPaginationParams, CursorPaginationResponse

        # 1. 验证文件夹是否存在
        folder = await crud.video_folder.get(db, id=folder_id)
        if not folder:
            raise HTTPException(status_code=404, detail="文件夹不存在")

        logger.info(
            f"DEBUG: 获取文件夹 {folder_id} 下的视频列表，状态: {status}, 游标: {cursor}, 大小: {size}"
        )

        # 2. 权限检查
        is_owner = current_user and current_user.id == folder.user_id
        is_admin = current_user and current_user.is_superuser
        can_view_all = is_owner or is_admin

        # 3. 构建分页参数
        pagination_params = CursorPaginationParams(
            cursor=str(cursor) if cursor else None,
            size=size,
            order_by="id",
        )

        # 4. 构建过滤条件
        filters = {"folder_id": folder_id}
        if status:
            filters["status"] = status

        # 5. 调用CRUD层进行分页查询
        paginated_result = await crud.video.get_paginated_videos(
            db=db,
            params=pagination_params,
            filters=filters,
            can_view_all=can_view_all,
        )

        # 6. 获取视频ID列表用于聚合
        video_ids = [video.id for video in paginated_result.items]
        logger.info(f"DEBUG: 从分页查询获取的视频ID列表: {video_ids}")

        # 7. 批量获取完整的视频信息
        video_out_list = await self._aggregate_videos_by_ids(
            db,
            video_ids=video_ids,
            current_user=current_user,
            include_review=False,
        )

        logger.info(f"DEBUG: 聚合后的视频列表长度: {len(video_out_list)}")

        # 8. 返回结果
        return CursorPaginationResponse[schemas.VideoOut](
            items=video_out_list,
            next_cursor=paginated_result.next_cursor,
            has_next=paginated_result.has_next,
            has_previous=bool(cursor > 0),
            previous_cursor=None,
            total_count=None,
        )


# 全局实例将在 service_factory 中创建和管理
