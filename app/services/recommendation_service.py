"""推荐服务实现"""

from __future__ import annotations

import json
import logging

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, schemas
from app.db.redis import (
    hash_get,
    sorted_set_add,
    sorted_set_get_range,
    sorted_set_get_score,
)
from app.schemas.recommendation import (
    PaginatedRecommendationResponse,
    RecommendationItem,
)
from app.services.recommendation_cache_service import RecommendationCacheService


logger = logging.getLogger(__name__)

recommendation_cache_service = RecommendationCacheService()


class RecommendationService:
    """推荐服务类"""

    async def _get_user_group(self, user_id: int) -> int | None:
        """从Redis获取用户推荐群组ID"""
        group_id_raw = await hash_get("user_to_group", str(user_id))
        return int(group_id_raw) if group_id_raw else None

    async def get_recommendations(
        self,
        db: AsyncSession,
        user_id: int,
        content_type: str,
        page: int,
        page_size: int,
    ) -> PaginatedRecommendationResponse:
        """获取推荐列表"""
        # 1. 获取用户所属的推荐群组
        user_group = await self._get_user_group(user_id)

        # 2. 从群组推荐池中获取内容
        recommendations = await self.get_recs_from_group_pool(
            user_id, user_group, content_type, page, page_size
        )

        # 3. 如果群组推荐不足，用热门内容补充
        if len(recommendations) < page_size:
            hot_recs = await self.get_recs_from_hot_pool(content_type, page, page_size)
            # 合并并去重
            existing_ids = {rec.content_id for rec in recommendations}
            for rec in hot_recs:
                if rec.content_id not in existing_ids:
                    recommendations.append(rec)
                    if len(recommendations) >= page_size:
                        break

        # 4. 如果仍然不足，用最新内容补充
        if len(recommendations) < page_size:
            latest_recs = await self.get_recs_from_latest_pool(content_type, page, page_size)
            existing_ids = {rec.content_id for rec in recommendations}
            for rec in latest_recs:
                if rec.content_id not in existing_ids:
                    recommendations.append(rec)
                    if len(recommendations) >= page_size:
                        break

        return PaginatedRecommendationResponse(
            items=recommendations, page=page, page_size=page_size
        )

    async def get_recs_from_group_pool(
        self,
        user_id: int,
        group_id: int | None,
        content_type: str,
        page: int,
        page_size: int,
    ) -> list[RecommendationItem]:
        """从按类型细化的群组推荐池中获取数据"""
        if group_id is None:
            logger.debug("用户 %s 未命中群组映射，跳过群组池召回", user_id)
            return []

        rec_pool_key = f"rec_pool:group:{group_id}:{content_type}"
        start = (page - 1) * page_size
        end = start + page_size - 1
        recs = await sorted_set_get_range(rec_pool_key, start, end, with_scores=True, desc=True)
        return [
            RecommendationItem(
                content_type=content_type, content_id=int(item), score=score
            )
            for item, score in recs
        ]

    async def get_recs_from_hot_pool(
        self, content_type: str, page: int, page_size: int
    ) -> list[RecommendationItem]:
        """从热门推荐池中获取数据"""
        try:
            rec_pool_key = f"rec_pool:hot:{content_type}"
            start = (page - 1) * page_size
            end = start + page_size - 1
            recs = await sorted_set_get_range(rec_pool_key, start, end, with_scores=True, desc=True)
            return [
                RecommendationItem(
                    content_type=content_type, content_id=int(item), score=score
                )
                for item, score in recs
            ]
        except Exception:
            return []

    async def get_recs_from_latest_pool(
        self, content_type: str, page: int, page_size: int
    ) -> list[RecommendationItem]:
        """从最新推荐池中获取数据"""
        try:
            rec_pool_key = f"rec_pool:latest:{content_type}"
            start = (page - 1) * page_size
            end = start + page_size - 1
            recs = await sorted_set_get_range(rec_pool_key, start, end, with_scores=True, desc=True)
            return [
                RecommendationItem(
                    content_type=content_type, content_id=int(item), score=score
                )
                for item, score in recs
            ]
        except Exception:
            return []

    async def add_impression(
        self, db: AsyncSession, user_id: int, recommendations: list[RecommendationItem]
    ):
        """记录曝光（用户看到了推荐）"""
        for rec in recommendations:
            interaction = schemas.UserInteractionCreate(
                content_type=rec.content_type,
                content_id=rec.content_id,
                interaction_type="impression",
                weight=0.5,
            )
            await crud.user_interaction.create_with_user(
                db=db, obj_in=interaction, user_id=user_id
            )

    async def add_user_feedback(
        self,
        db: AsyncSession,
        user_id: int,
        content_id: int,
        content_type: str,
        feedback_type: str,
    ) -> None:
        """记录用户反馈并更新推荐权重"""
        await self.record_feedback(
            db=db,
            user_id=user_id,
            recommendation_log_id=None,
            feedback_type=feedback_type,
            content_type=content_type,
            content_id=content_id,
            reason=None,
        )

        if feedback_type in {"like", "favorite", "dislike", "not_interested"}:
            await self.update_user_profile_from_feedback(
                db=db,
                user_id=user_id,
                content_type=content_type,
                content_id=content_id,
                feedback_type=feedback_type,
            )

        await self.invalidate_user_cache(user_id)

    async def update_recommendation_score(
        self, user_id: int, content_id: int, content_type: str, feedback_type: str
    ):
        """根据用户反馈更新推荐分数"""
        # 定义不同反馈类型的分数权重
        score_weights = {
            "click": 1.0,
            "like": 2.0,
            "favorite": 3.0,
            "comment": 4.0,
            "share": 5.0,
        }
        score_increment = score_weights.get(feedback_type, 0)

        if score_increment > 0:
            # 更新热门推荐池
            hot_pool_key = f"rec_pool:hot:{content_type}"
            await sorted_set_add(hot_pool_key, {str(content_id): score_increment})

            # 更新用户所属群组的推荐池
            user_group = await self._get_user_group(user_id)
            if user_group:
                group_pool_key = f"rec_pool:group:{user_group}:{content_type}"
                await sorted_set_add(group_pool_key, {str(content_id): score_increment})

    async def get_content_score(self, content_type: str, content_id: int) -> float | None:
        """获取内容的推荐分数"""
        try:
            item_key = f"{content_type}:{content_id}"
            score = await sorted_set_get_score("rec_pool:hot:all", item_key)
            return score
        except Exception:
            return None

    async def update_hot_pool(self, content_type: str, content_id: int, score: float):
        """更新热门推荐池"""
        try:
            rec_pool_key = f"rec_pool:hot:{content_type}"
            await sorted_set_add(rec_pool_key, {str(content_id): score})
        except Exception:
            pass

    async def get_hot_content(
        self,
        content_type: str,
        limit: int,
        offset: int = 0,
    ) -> PaginatedRecommendationResponse:
        """获取热门内容（支持偏移量分页）

        Args:
            content_type: 内容类型 ("article" 或 "video")
            limit: 每页大小
            offset: 偏移量

        Returns:
            PaginatedRecommendationResponse: 分页的推荐响应
        """
        try:
            rec_pool_key = f"rec_pool:hot:{content_type}"
            start = offset
            end = start + limit - 1

            # 调试信息：记录Redis查询参数
            logger.debug("Redis查询参数 - key=%s, start=%s, end=%s", rec_pool_key, start, end)

            # 获取比需要多一个的数据，用于判断是否有下一页
            extra_items = await sorted_set_get_range(
                rec_pool_key, start, end + 1, with_scores=True, desc=True
            )

            # 调试信息：Redis返回结果
            logger.debug("Redis返回 - 原始数据数量: %s", len(extra_items))
            if extra_items:
                logger.debug("Redis返回前5条数据: %s", extra_items[:5])
            else:
                logger.warning(
                    "Redis key '%s' 为空，请检查热门推荐池是否已初始化",
                    rec_pool_key,
                )

            # 判断是否有下一页
            has_next = len(extra_items) > limit

            # 取实际需要的数据
            items_data = extra_items[:limit]

            # 转换为 RecommendationItem 对象
            items = []
            for item, score in items_data:
                try:
                    items.append(
                        RecommendationItem(
                            content_type=content_type,
                            content_id=int(item),
                            score=float(score),
                            reason="hot_content",
                        )
                    )
                except (ValueError, TypeError) as e:
                    logger.warning(
                        "数据转换失败 - item=%s, score=%s, error=%s",
                        item,
                        score,
                        e,
                    )
                    continue

            logger.debug("成功转换推荐项数量: %s", len(items))

            return PaginatedRecommendationResponse(
                items=items,
                has_next=has_next,
            )

        except Exception:
            # 调试信息：记录异常
            logger.exception("get_hot_content 异常 - content_type=%s", content_type)
            # 降级返回空结果
            return PaginatedRecommendationResponse(
                items=[],
                has_next=False,
            )

    async def get_paginated_content_recommendations(
        self,
        user_id: int,
        content_type: str,
        page: int,
        limit: int,
    ) -> list[RecommendationItem]:
        """获取分页的内容推荐（兼容游标分页调用）

        Args:
            user_id: 用户ID
            content_type: 内容类型
            page: 页码（从1开始）
            limit: 每页大小

        Returns:
            推荐项列表
        """
        try:
            # 调用现有的推荐方法
            result = await self.get_recommendations(
                db=None,  # 这个方法不需要db
                user_id=user_id,
                content_type=content_type,
                page=page,
                page_size=limit,
            )
            return result.items
        except Exception:
            return []

    async def get_similar_content(
        self,
        content_type: str,
        content_id: int,
        limit: int,
    ) -> list[RecommendationItem]:
        """获取相似内容

        Args:
            content_type: 内容类型
            content_id: 内容ID
            limit: 返回数量限制

        Returns:
            相似内容推荐项列表
        """
        try:
            # 从相似内容池获取数据
            rec_pool_key = f"rec_pool:similar:{content_type}:{content_id}"
            recs = await sorted_set_get_range(
                rec_pool_key, 0, limit - 1, with_scores=True, desc=True
            )

            items = []
            for item, score in recs:
                try:
                    items.append(
                        RecommendationItem(
                            content_type=content_type,
                            content_id=int(item),
                            score=float(score),
                            reason="similar_content",
                        )
                    )
                except (ValueError, TypeError):
                    continue

            return items
        except Exception:
            return []

    async def invalidate_user_cache(self, user_id: int) -> None:
        """清除指定用户的推荐缓存"""
        await recommendation_cache_service.invalidate_user_cache(user_id)

    # ------------------------------------------------------------------
    # 反馈与画像
    # ------------------------------------------------------------------

    async def record_feedback(
        self,
        db: AsyncSession,
        user_id: int,
        feedback_type: str,
        content_type: str,
        content_id: int,
        recommendation_log_id: int | None = None,
        reason: str | None = None,
    ) -> None:
        """记录用户反馈数据并更新推荐日志"""
        weight = self._get_feedback_weight(feedback_type)

        interaction = schemas.UserInteractionCreate(
            content_type=content_type,
            content_id=content_id,
            interaction_type=feedback_type,
            weight=weight,
            extra_data=json.dumps(
                {
                    "recommendation_log_id": recommendation_log_id,
                    "reason": reason,
                },
                ensure_ascii=False,
            ),
        )
        await crud.user_interaction.create_with_user(db=db, obj_in=interaction, user_id=user_id)

        if recommendation_log_id:
            await crud.recommendation_log.update_click_status(
                db=db, log_id=recommendation_log_id, content_id=content_id
            )

        await self.update_recommendation_score(user_id, content_id, content_type, feedback_type)

    async def update_user_profile_from_feedback(
        self,
        db: AsyncSession,
        user_id: int,
        content_type: str,
        content_id: int,
        feedback_type: str,
    ) -> None:
        """根据反馈调整用户画像"""
        profile = await crud.user_profile.get_by_user_id(db=db, user_id=user_id)
        if not profile:
            return

        try:
            interest_tags = json.loads(profile.interest_tags) if profile.interest_tags else {}
        except json.JSONDecodeError:
            interest_tags = {}

        try:
            preferred_categories = (
                json.loads(profile.preferred_categories)
                if profile.preferred_categories
                else {}
            )
        except json.JSONDecodeError:
            preferred_categories = {}

        key = f"{content_type}:{content_id}"
        delta = 0.3 if feedback_type == "like" else -0.5 if feedback_type in {"dislike", "not_interested"} else 0.0
        if delta != 0.0:
            preferred_categories[key] = max(0.0, preferred_categories.get(key, 0.0) + delta)

        update_data = schemas.UserProfileUpdate(
            preferred_categories=json.dumps(preferred_categories, ensure_ascii=False),
            interest_tags=json.dumps(interest_tags, ensure_ascii=False)
            if interest_tags
            else None,
        )
        await crud.user_profile.update(db=db, db_obj=profile, obj_in=update_data)

    @staticmethod
    def _get_feedback_weight(feedback_type: str) -> float:
        weights = {
            "click": 1.0,
            "like": 2.0,
            "favorite": 3.0,
            "comment": 4.0,
            "share": 5.0,
            "dislike": -1.0,
            "not_interested": -0.5,
            "report": -2.0,
        }
        return weights.get(feedback_type, 0.0)
