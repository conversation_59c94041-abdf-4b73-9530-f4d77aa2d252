"""推荐系统API接口"""

import logging
from datetime import datetime, timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.permission_system import (
    Action,
    PermissionChecker,
    ResourceType,
    Scope,
)
from app.core.permission_system import (
    Permission as PermissionObject,
)
from app.services.recommendation_evaluation_service import RecommendationEvaluationService
from app.services.service_factory import (
    get_recommendation_evaluation_service,
    get_recommendation_cache_service,
    get_user_aggregation_service,
    get_user_recommendation_service,
)
from app.services.user_aggregation_service import UserAggregationService
from app.services.user_recommendation_service import UserRecommendationService
from app.services.recommendation_cache_service import RecommendationCacheService
from app.models.user import user_follow

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/stats", response_model=schemas.RecommendationStats)
async def get_recommendation_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> Any:
    """获取推荐统计信息（仅管理员可访问）"""
    from sqlalchemy import func, select

    # 获取推荐统计数据
    total_recommendations_result = await db.execute(
        select(func.count(models.RecommendationLog.id)).where(
            models.RecommendationLog.created_at >= datetime.utcnow() - timedelta(days=days)
        )
    )
    total_recommendations = total_recommendations_result.scalar()

    # 计算总体点击率
    overall_ctr = await crud.recommendation_log.get_click_through_rate(db, days=days)

    # 按算法类型统计性能
    algorithm_performance = {}
    for algorithm in ["collaborative", "content_based", "hot", "hybrid"]:
        ctr = await crud.recommendation_log.get_click_through_rate(
            db, algorithm_type=algorithm, days=days
        )
        algorithm_performance[algorithm] = {"click_through_rate": ctr}

    # 统计热门内容类型
    popular_content_types = {
        "article": 0,
        "video": 0,
    }

    # 这里可以添加更详细的统计逻辑

    return schemas.RecommendationStats(
        total_recommendations=total_recommendations,
        click_through_rate=overall_ctr,
        algorithm_performance=algorithm_performance,
        popular_content_types=popular_content_types,
    )


@router.get("/evaluation/report", response_model=dict)
async def get_evaluation_report(
    *,
    db: AsyncSession = Depends(deps.get_db),
    days: int = Query(7, ge=1, le=30, description="评估天数"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
    recommendation_evaluation_service: RecommendationEvaluationService = Depends(
        get_recommendation_evaluation_service
    ),
) -> Any:
    """获取推荐效果评估报告（仅管理员可访问）"""
    # 生成综合评估报告
    report = await recommendation_evaluation_service.generate_comprehensive_report(db, days=days)

    return report


@router.get("/evaluation/algorithm/{algorithm_type}", response_model=dict)
async def get_algorithm_evaluation(
    *,
    db: AsyncSession = Depends(deps.get_db),
    algorithm_type: str,
    days: int = Query(7, ge=1, le=30, description="评估天数"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
    recommendation_evaluation_service: RecommendationEvaluationService = Depends(
        get_recommendation_evaluation_service
    ),
) -> Any:
    """获取特定算法的评估结果（仅管理员可访问）"""
    # 验证算法类型
    if algorithm_type not in ["collaborative", "content_based", "hot", "hybrid"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的算法类型",
        )

    # 计算各项指标
    ctr_data = await recommendation_evaluation_service.calculate_click_through_rate(
        db, algorithm_type=algorithm_type, days=days
    )

    diversity_data = await recommendation_evaluation_service.calculate_diversity_score(
        db, algorithm_type=algorithm_type, days=days
    )

    coverage_data = await recommendation_evaluation_service.calculate_coverage_score(
        db, algorithm_type=algorithm_type, days=days
    )

    return {
        "algorithm_type": algorithm_type,
        "evaluation_period": f"{days} days",
        "click_through_rate": ctr_data,
        "diversity": diversity_data,
        "coverage": coverage_data,
        "generated_at": datetime.utcnow().isoformat(),
    }


@router.get("/evaluation/user/{user_id}", response_model=dict)
async def get_user_evaluation(
    *,
    db: AsyncSession = Depends(deps.get_db),
    user_id: int,
    algorithm_type: str = Query("hybrid", description="算法类型"),
    days: int = Query(7, ge=1, le=30, description="评估天数"),
    current_user: models.User = Depends(deps.get_current_active_user),
    recommendation_evaluation_service: RecommendationEvaluationService = Depends(
        get_recommendation_evaluation_service
    ),
) -> Any:
    """获取特定用户的推荐效果评估（仅管理员或用户本人可访问）"""

    # 检查权限：管理员或用户本人
    is_admin = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.SYSTEM, action=Action.MANAGE, scope=Scope.ALL),
    )
    if not (is_admin or current_user.id == user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的评估数据",
        )

    # 计算用户相关指标
    precision_recall = await recommendation_evaluation_service.calculate_precision_recall(
        db, user_id=user_id, algorithm_type=algorithm_type, days=days
    )

    novelty_data = await recommendation_evaluation_service.calculate_novelty_score(
        db, user_id=user_id, algorithm_type=algorithm_type, days=days
    )

    return {
        "user_id": user_id,
        "algorithm_type": algorithm_type,
        "evaluation_period": f"{days} days",
        "precision_recall": precision_recall,
        "novelty": novelty_data,
        "generated_at": datetime.utcnow().isoformat(),
    }


# 用户推荐相关API端点
@router.get("/users", response_model=schemas.UserRecommendationResponse)
async def get_user_recommendations(
    *,
    db: AsyncSession = Depends(deps.get_db),
    algorithm_type: str | None = Query(None, description="推荐算法类型"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=50, description="每页大小"),
    current_user: models.User = Depends(deps.get_current_user),
    user_recommendation_service: UserRecommendationService = Depends(
        get_user_recommendation_service
    ),
    user_aggregation_service: UserAggregationService = Depends(get_user_aggregation_service),
) -> Any:
    """获取用户推荐列表"""

    try:
        # 获取推荐结果
        recommendation_response = await user_recommendation_service.get_user_recommendations(
            db=db,
            user_id=current_user.id,
            page=page,
            page_size=page_size,
            algorithm_type=algorithm_type,
        )

        # 获取推荐用户的详细信息
        user_ids = [item.content_id for item in recommendation_response.items]
        if not user_ids:
            return schemas.UserRecommendationResponse(
                items=[],
                total_count=0,
                page=page,
                page_size=page_size,
                has_next=False,
                algorithm_type=algorithm_type or "hybrid",
                generated_at=datetime.utcnow(),
            )

        # 批量获取用户信息
        users_data = await user_aggregation_service.get_users_by_ids(db=db, user_ids=user_ids)

        # 计算共同关注数
        mutual_follow_counts: dict[int, int] = {}
        following_user_ids: set[int] = set()
        if user_ids:
            from sqlalchemy import func, select

            follow_alias_1 = user_follow.alias("follow_alias_1")
            follow_alias_2 = user_follow.alias("follow_alias_2")

            mutual_stmt = (
                select(
                    follow_alias_2.c.follower_id.label("target_user_id"),
                    func.count(func.distinct(follow_alias_1.c.followed_id)).label("mutual_count"),
                )
                .select_from(
                    follow_alias_1.join(
                        follow_alias_2,
                        follow_alias_1.c.followed_id == follow_alias_2.c.followed_id,
                    )
                )
                .where(
                    follow_alias_1.c.follower_id == current_user.id,
                    follow_alias_2.c.follower_id.in_(user_ids),
                )
                .group_by(follow_alias_2.c.follower_id)
            )

            mutual_result = await db.execute(mutual_stmt)
            mutual_follow_counts = {
                row["target_user_id"]: int(row["mutual_count"] or 0)
                for row in mutual_result.mappings().all()
            }

            # 查询当前登录用户对推荐用户的关注状态
            following_stmt = (
                select(user_follow.c.followed_id)
                .where(
                    user_follow.c.follower_id == current_user.id,
                    user_follow.c.followed_id.in_(user_ids),
                )
            )
            following_result = await db.execute(following_stmt)
            following_user_ids = set(following_result.scalars().all())

        # 构建推荐项
        recommendation_items = []
        for rec_item in recommendation_response.items:
            user_data = users_data.get(rec_item.content_id)
            if user_data:
                mutual_count = mutual_follow_counts.get(user_data.id, 0)

                recommendation_items.append(
                    schemas.UserRecommendationItem(
                        user_id=user_data.id,
                        username=user_data.username,
                        nickname=user_data.nickname,
                        avatar=user_data.avatar,
                        description=user_data.description,
                        follower_count=user_data.stats.follower_count if user_data.stats else 0,
                        following_count=user_data.stats.following_count if user_data.stats else 0,
                        score=rec_item.score,
                        reason=rec_item.reason,
                        algorithm_type=rec_item.algorithm_type,
                        mutual_following_count=mutual_count,
                        is_following=user_data.id in following_user_ids,
                    )
                )

        return schemas.UserRecommendationResponse(
            items=recommendation_items,
            total_count=recommendation_response.total_count,
            page=page,
            page_size=page_size,
            has_next=recommendation_response.has_next,
            algorithm_type=algorithm_type or "hybrid",
            generated_at=datetime.utcnow(),
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户推荐失败: {str(e)}",
        )


@router.post("/users/feedback")
async def submit_user_recommendation_feedback(
    *,
    db: AsyncSession = Depends(deps.get_db),
    feedback: schemas.UserRecommendationFeedback,
    current_user: models.User = Depends(deps.get_current_user),
    recommendation_cache_service: RecommendationCacheService = Depends(
        get_recommendation_cache_service
    ),
) -> dict[str, str]:
    """提交用户推荐反馈"""
    try:
        # 记录推荐反馈
        feedback_data = schemas.RecommendationLogCreate(
            algorithm_type=feedback.algorithm_type,
            recommended_items=f"user:{feedback.user_id}",
            recommendation_reason=f"feedback:{feedback.feedback_type}",
            position=str(feedback.position) if feedback.position else None,
        )

        await crud.recommendation_log.create_with_user(
            db=db, obj_in=feedback_data, user_id=current_user.id
        )

        # 如果是关注反馈，执行关注操作
        if feedback.feedback_type == "follow":
            if feedback.user_id == current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能关注自己",
                )

            followed_user = await crud.user.get(db, id=feedback.user_id)
            if not followed_user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="被推荐的用户不存在",
                )

            # 检查是否已关注
            from sqlalchemy import and_, select

            follow_exists_stmt = select(user_follow.c.followed_id).where(
                and_(
                    user_follow.c.follower_id == current_user.id,
                    user_follow.c.followed_id == feedback.user_id,
                )
            )

            follow_exists_result = await db.execute(follow_exists_stmt)
            already_following = follow_exists_result.first() is not None

            if not already_following:
                follower = await crud.user.get(db, id=current_user.id)
                if not follower:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="当前用户信息无效",
                    )

                await crud.user.follow(db, follower=follower, followed=followed_user)

                from app.services.user_recommendation_tracking_service import (
                    UserRecommendationTrackingService,
                )

                tracking_service = UserRecommendationTrackingService()
                await tracking_service.track_follow_conversion(
                    db=db,
                    user_id=current_user.id,
                    followed_user_id=feedback.user_id,
                    algorithm_type=feedback.algorithm_type,
                    source=str(feedback.position) if feedback.position is not None else "feedback",
                    extra_data=feedback.extra_data,
                )

                # 关注成功后使推荐缓存失效，确保下次请求返回最新结果
                try:
                    await recommendation_cache_service.invalidate_user_recommendations_cache(
                        current_user.id
                    )
                except Exception as cache_error:  # noqa: BLE001
                    logger.warning(
                        "Failed to invalidate recommendation cache for user %s: %s",
                        current_user.id,
                        cache_error,
                    )

        return {"message": "反馈提交成功"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交反馈失败: {str(e)}",
        )


@router.get("/users/similarity/{target_user_id}")
async def get_user_similarity(
    *,
    db: AsyncSession = Depends(deps.get_db),
    target_user_id: int,
    similarity_types: list[str] = Query(["overall"], description="相似度类型列表"),
    current_user: models.User = Depends(deps.get_current_user),
) -> dict[str, Any]:
    """获取与指定用户的相似度"""
    from app.services.user_similarity_service import UserSimilarityService

    user_similarity_service = UserSimilarityService()

    try:
        # 检查目标用户是否存在
        target_user = await crud.user.get(db, id=target_user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="目标用户不存在",
            )

        # 计算相似度
        similarities = await user_similarity_service.calculate_user_similarity(
            db=db,
            user_id1=current_user.id,
            user_id2=target_user_id,
            similarity_types=similarity_types,
        )

        return {
            "current_user_id": current_user.id,
            "target_user_id": target_user_id,
            "similarities": similarities,
            "calculated_at": datetime.utcnow().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"计算用户相似度失败: {str(e)}",
        )


# 用户推荐追踪相关API端点
@router.post("/users/track/display")
async def track_recommendation_display(
    *,
    db: AsyncSession = Depends(deps.get_db),
    display_data: dict[str, Any],
    current_user: models.User = Depends(deps.get_current_user),
) -> dict[str, str]:
    """追踪推荐展示事件"""
    from app.services.user_recommendation_tracking_service import UserRecommendationTrackingService

    tracking_service = UserRecommendationTrackingService()

    try:
        success = await tracking_service.track_recommendation_display(
            db=db,
            user_id=current_user.id,
            recommended_user_ids=display_data.get("recommended_user_ids", []),
            algorithm_type=display_data.get("algorithm_type", "unknown"),
            position=display_data.get("position"),
            extra_data=display_data.get("extra_data"),
        )

        if success:
            return {"message": "展示事件追踪成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="展示事件追踪失败",
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"追踪展示事件失败: {str(e)}",
        )


@router.post("/users/track/click")
async def track_recommendation_click(
    *,
    db: AsyncSession = Depends(deps.get_db),
    click_data: dict[str, Any],
    current_user: models.User = Depends(deps.get_current_user),
) -> dict[str, str]:
    """追踪推荐点击事件"""
    from app.services.user_recommendation_tracking_service import UserRecommendationTrackingService

    tracking_service = UserRecommendationTrackingService()

    try:
        success = await tracking_service.track_recommendation_click(
            db=db,
            user_id=current_user.id,
            clicked_user_id=click_data.get("clicked_user_id"),
            algorithm_type=click_data.get("algorithm_type", "unknown"),
            position=click_data.get("position"),
            extra_data=click_data.get("extra_data"),
        )

        if success:
            return {"message": "点击事件追踪成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="点击事件追踪失败",
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"追踪点击事件失败: {str(e)}",
        )


@router.get("/users/metrics")
async def get_user_recommendation_metrics(
    *,
    db: AsyncSession = Depends(deps.get_db),
    algorithm_type: str | None = Query(None, description="算法类型"),
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> dict[str, Any]:
    """获取用户推荐效果指标（仅管理员可访问）"""
    from app.services.user_recommendation_tracking_service import UserRecommendationTrackingService

    tracking_service = UserRecommendationTrackingService()

    try:
        metrics = await tracking_service.get_recommendation_metrics(
            db=db,
            algorithm_type=algorithm_type,
            days=days,
        )

        return metrics

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取推荐指标失败: {str(e)}",
        )


@router.get("/users/metrics/comparison")
async def get_algorithm_comparison(
    *,
    db: AsyncSession = Depends(deps.get_db),
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> dict[str, Any]:
    """获取算法效果对比（仅管理员可访问）"""
    from app.services.user_recommendation_tracking_service import UserRecommendationTrackingService

    tracking_service = UserRecommendationTrackingService()

    try:
        comparison = await tracking_service.get_algorithm_comparison(
            db=db,
            days=days,
        )

        return comparison

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取算法对比失败: {str(e)}",
        ) from e
