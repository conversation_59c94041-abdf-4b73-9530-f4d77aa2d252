import enum
from datetime import UTC, datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Enum, <PERSON><PERSON><PERSON>, Integer, String, Text, event
from sqlalchemy.orm import relationship

from app.core.logging import logger
from app.db.session import Base
from app.db.timestamp import Timestamp

listens_for = event.listens_for


class NotificationType(enum.Enum):
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    TASK_COMPLETE = "task_complete"
    NEW_MESSAGE = "new_message"
    SYSTEM_UPDATE = "system_update"
    CONTENT_RECOMMENDATION = "content_recommendation"
    ACCOUNT_ACTIVITY = "account_activity"
    # 沸点相关通知
    POST_LIKE = "post_like"  # 沸点被点赞
    POST_COMMENT = "post_comment"  # 沸点被评论
    POST_REPOST = "post_repost"  # 沸点被转发
    POST_MENTION = "post_mention"  # 在沸点中被@提及
    POST_POLL_VOTE = "post_poll_vote"  # 投票沸点有新投票


# 添加运行时验证函数，用于调试枚举问题
def validate_notification_type_enum():
    """检查枚举定义是否正确"""
    logger.debug("NotificationType enum members: %s", list(NotificationType))
    logger.debug(
        "NotificationType enum values: %s", [e.value for e in NotificationType]
    )
    logger.debug(
        "SYSTEM_UPDATE in enum: %s", NotificationType.SYSTEM_UPDATE in NotificationType
    )

    # 测试字符串转换
    try:
        test_result = NotificationType("system_update")
        logger.debug("String conversion test: 'system_update' -> %s", test_result)
    except ValueError as e:
        logger.warning("String conversion test failed: %s", e)

    return True


class NotificationPriority(enum.Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class NotificationStatus(enum.Enum):
    UNREAD = "unread"
    READ = "read"
    DELETED = "deleted"


class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    type = Column(
        Enum(
            NotificationType,
            name="notificationtype",
            native_enum=True,
            values_callable=lambda enum_cls: [e.value for e in enum_cls],
        ),
        nullable=False,
        index=True,
    )
    priority = Column(
        Enum(
            NotificationPriority,
            name="notificationpriority",
            native_enum=True,
            values_callable=lambda enum_cls: [e.value for e in enum_cls],
        ),
        default=NotificationPriority.NORMAL,
        index=True,
    )
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    status = Column(
        Enum(
            NotificationStatus,
            name="notificationstatus",
            native_enum=True,
            values_callable=lambda enum_cls: [e.value for e in enum_cls],
        ),
        default=NotificationStatus.UNREAD,
        index=True,
    )
    data = Column(JSON)  # 附加数据，如任务ID、内容ID等
    action_url = Column(String(500))  # 可点击的链接
    expires_at = Column(DateTime)  # 过期时间
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=True)
    updated_at = Column(
        Timestamp, default=datetime.now(UTC), onupdate=datetime.now(UTC), nullable=True
    )
    read_at = Column(DateTime)

    # 关系
    user = relationship("User", back_populates="notifications")

    def __repr__(self):
        return f"<Notification(id={self.id}, user_id={self.user_id}, type={self.type}, title='{self.title}')>"


# SQLAlchemy 事件处理器 - 修复枚举映射问题
@listens_for(Notification, "load")
def fix_notification_enums_on_load(target, context):
    """修复从数据库加载时的枚举映射问题"""
    try:
        # 如果是从数据库加载的，type可能是字符串，需要转换为枚举
        if isinstance(target.type, str) and target.type in [e.value for e in NotificationType]:
            logger.debug(
                "Fixing type conversion: '%s' -> %s",
                target.type,
                NotificationType(target.type),
            )
            target.type = NotificationType(target.type)

        if isinstance(target.priority, str) and target.priority in [
            e.value for e in NotificationPriority
        ]:
            logger.debug(
                "Fixing priority conversion: '%s' -> %s",
                target.priority,
                NotificationPriority(target.priority),
            )
            target.priority = NotificationPriority(target.priority)

        if isinstance(target.status, str) and target.status in [
            e.value for e in NotificationStatus
        ]:
            logger.debug(
                "Fixing status conversion: '%s' -> %s",
                target.status,
                NotificationStatus(target.status),
            )
            target.status = NotificationStatus(target.status)

    except Exception as e:
        logger.warning("Error in enum fix listener: %s", e)


@listens_for(Notification, "before_insert")
def ensure_enum_values_before_insert(mapper, connection, instance):
    """在插入前确保枚举值为字符串类型以便存储"""
    try:
        # 确保写入数据库的值是字符串类型
        if hasattr(instance.type, "value"):
            instance.type = instance.type.value
        if hasattr(instance.priority, "value"):
            instance.priority = instance.priority.value
        if hasattr(instance.status, "value"):
            instance.status = instance.status.value
    except Exception as e:
        logger.warning("Error in enum value preparation: %s", e)


@listens_for(Notification, "before_update")
def ensure_enum_values_before_update(mapper, connection, instance):
    """在更新前确保枚举值为字符串类型以便存储"""
    try:
        # 确保写入数据库的值是字符串类型
        if hasattr(instance.type, "value"):
            instance.type = instance.type.value
        if hasattr(instance.priority, "value"):
            instance.priority = instance.priority.value
        if hasattr(instance.status, "value"):
            instance.status = instance.status.value
    except Exception as e:
        logger.warning("Error in enum value preparation: %s", e)
