"""通用异常处理器"""

from fastapi import HTTPException, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.core.logging import logger
from app.core.response_wrapper import ResponseCode, error_response


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """处理标准HTTP异常"""

    logger.warning(
        f"HTTP exception: {exc.status_code} - {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "detail": exc.detail,
            "path": request.url.path,
            "method": request.method,
        },
    )

    # 映射HTTP状态码到业务状态码
    status_code_mapping = {
        400: ResponseCode.BAD_REQUEST,
        401: ResponseCode.UNAUTHORIZED,
        403: ResponseCode.FORBIDDEN,
        404: ResponseCode.NOT_FOUND,
        422: ResponseCode.VALIDATION_ERROR,
        500: ResponseCode.INTERNAL_SERVER_ERROR,
        503: ResponseCode.SERVICE_UNAVAILABLE,
    }

    response_code = status_code_mapping.get(exc.status_code, ResponseCode.FAILURE)

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=response_code, message=str(exc.detail), data={"detail": exc.detail}
        ),
    )


async def starlette_http_exception_handler(
    request: Request, exc: StarletteHTTPException
) -> JSONResponse:
    """处理Starlette HTTP异常"""

    logger.warning(
        f"Starlette HTTP exception: {exc.status_code} - {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "detail": exc.detail,
            "path": request.url.path,
            "method": request.method,
        },
    )

    # 映射HTTP状态码到业务状态码
    status_code_mapping = {
        400: ResponseCode.BAD_REQUEST,
        401: ResponseCode.UNAUTHORIZED,
        403: ResponseCode.FORBIDDEN,
        404: ResponseCode.NOT_FOUND,
        422: ResponseCode.VALIDATION_ERROR,
        500: ResponseCode.INTERNAL_SERVER_ERROR,
        503: ResponseCode.SERVICE_UNAVAILABLE,
    }

    response_code = status_code_mapping.get(exc.status_code, ResponseCode.FAILURE)

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=response_code, message=str(exc.detail), data={"detail": exc.detail}
        ),
    )


async def validation_exception_handler(
    request: Request, exc: RequestValidationError
) -> JSONResponse:
    """处理请求验证异常"""

    def sanitize_value(value):
        """递归清理不能序列化的值"""
        if isinstance(value, bytes):
            return "<binary data>"
        elif isinstance(value, dict):
            return {k: sanitize_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [sanitize_value(item) for item in value]
        elif isinstance(value, tuple):
            return tuple(sanitize_value(item) for item in value)
        else:
            return value

    def sanitize_error(error: dict) -> dict:
        """清理错误信息中不能序列化的内容"""
        return {key: sanitize_value(value) for key, value in error.items()}

    errors = [sanitize_error(error) for error in exc.errors()]

    logger.warning(
        "Request validation failed",
        extra={
            "errors": errors,
            "path": request.url.path,
            "method": request.method,
        },
    )

    return JSONResponse(
        status_code=422,
        content=error_response(
            code=ResponseCode.VALIDATION_ERROR,
            message="请求参数验证失败",
            data={"validation_errors": errors},
        ),
    )


async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """处理通用异常"""

    logger.opt(exception=exc).error(
        "Unexpected error while processing request",
        extra={
            "exception_type": type(exc).__name__,
            "exception_message": str(exc),
            "path": request.url.path,
            "method": request.method,
        },
    )

    return JSONResponse(
        status_code=500,
        content=error_response(code=ResponseCode.INTERNAL_SERVER_ERROR, message="系统内部错误"),
    )
